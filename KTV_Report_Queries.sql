-- ====================================================================
-- KTV每日报告数据检索查询示例
-- 展示如何有效检索合并数据用于报告目的
-- ====================================================================

USE OperateData;
GO

-- 查询1: 总览数据报告
SELECT 
    日期,
    门店,
    星期,
    营收_总收入,
    营收_白天档,
    营收_晚上档,
    带客_全天总批数,
    带客_白天档_总批次,
    带客_晚上档_总批次,
    带客_白天档_直落,
    带客_晚上档_直落,
    用餐_总人数,
    用餐_自助餐人数,
    用餐_直落人数
FROM dbo.KTV_DailyReport_Comprehensive
ORDER BY 日期;

-- 查询2: 特定时段数据报告
SELECT 
    日期,
    门店,
    [10:50-13:50_K+] AS '早午档_K+',
    [10:50-13:50_特权预约] AS '早午档_特权',
    [10:50-13:50_小计] AS '早午档_小计',
    [10:50-13:50_上一档直落] AS '早午档_直落',
    [15:00-18:00_K+] AS '下午档_K+',
    [15:00-18:00_特权预约] AS '下午档_特权',
    [15:00-18:00_小计] AS '下午档_小计',
    [15:00-18:00_上一档直落] AS '下午档_直落',
    [18:10-21:10_K+] AS '晚间档_K+',
    [18:10-21:10_特权预约] AS '晚间档_特权',
    [18:10-21:10_小计] AS '晚间档_小计',
    [18:10-21:10_上一档直落] AS '晚间档_直落'
FROM dbo.KTV_DailyReport_Comprehensive
ORDER BY 日期;

-- 查询3: 综合分析报告（总览+关键时段）
SELECT 
    日期,
    门店,
    星期,
    营收_总收入,
    营收_白天档,
    带客_全天总批数,
    用餐_总人数,
    [11:50-14:50_K+] + [15:00-18:00_K+] AS '主力时段_K+总数',
    [11:50-14:50_小计] + [15:00-18:00_小计] AS '主力时段_总批次',
    [11:50-14:50_上一档直落] + [15:00-18:00_上一档直落] AS '主力时段_直落总数',
    CASE 
        WHEN 营收_总收入 > 120000 THEN '高收入日'
        WHEN 营收_总收入 > 100000 THEN '中等收入日'
        ELSE '低收入日'
    END AS '收入等级'
FROM dbo.KTV_DailyReport_Comprehensive
ORDER BY 日期;

-- 查询4: 时段效率分析
SELECT 
    日期,
    门店,
    [10:50-13:50_小计] AS '时段1_批次',
    [11:50-14:50_小计] AS '时段2_批次',
    [13:30-16:30_小计] AS '时段3_批次',
    [14:00-17:00_小计] AS '时段4_批次',
    [15:00-18:00_小计] AS '时段5_批次',
    [10:50-13:50_小计] + [11:50-14:50_小计] + [13:30-16:30_小计] + [14:00-17:00_小计] + [15:00-18:00_小计] AS '白天时段_总批次',
    ROUND(
        CAST([10:50-13:50_小计] + [11:50-14:50_小计] + [13:30-16:30_小计] + [14:00-17:00_小计] + [15:00-18:00_小计] AS FLOAT) / 
        NULLIF(带客_白天档_总批次, 0) * 100, 2
    ) AS '时段覆盖率_%'
FROM dbo.KTV_DailyReport_Comprehensive
ORDER BY 日期;

-- 查询5: 上一档直落分析
SELECT 
    日期,
    门店,
    [10:50-13:50_上一档直落] AS '10:50直落',
    [11:50-14:50_上一档直落] AS '11:50直落',
    [13:30-16:30_上一档直落] AS '13:30直落',
    [14:00-17:00_上一档直落] AS '14:00直落',
    [15:00-18:00_上一档直落] AS '15:00直落',
    [16:50-19:50_上一档直落] AS '16:50直落',
    [17:10-20:10_上一档直落] AS '17:10直落',
    [18:10-21:10_上一档直落] AS '18:10直落',
    [19:00-22:00_上一档直落] AS '19:00直落',
    [19:30-22:30_上一档直落] AS '19:30直落',
    [10:50-13:50_上一档直落] + [11:50-14:50_上一档直落] + [13:30-16:30_上一档直落] + [14:00-17:00_上一档直落] + [15:00-18:00_上一档直落] + [16:50-19:50_上一档直落] + [17:10-20:10_上一档直落] + [18:10-21:10_上一档直落] + [19:00-22:00_上一档直落] + [19:30-22:30_上一档直落] AS '总直落数'
FROM dbo.KTV_DailyReport_Comprehensive
ORDER BY 日期;

-- 查询6: 日期范围查询
SELECT 
    日期,
    门店,
    营收_总收入,
    带客_全天总批数,
    用餐_总人数,
    [11:50-14:50_小计] AS '午间主力档',
    [15:00-18:00_小计] AS '下午主力档'
FROM dbo.KTV_DailyReport_Comprehensive
WHERE 日期 BETWEEN '20250502' AND '20250505'
ORDER BY 日期;

-- 查询7: 表结构信息
SELECT 
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型',
    IS_NULLABLE AS '允许空值',
    COLUMN_DEFAULT AS '默认值'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'KTV_DailyReport_Comprehensive'
ORDER BY ORDINAL_POSITION;

PRINT N'KTV每日报告查询示例完成';
PRINT N'表包含总览数据和完整时段数据，支持灵活的报告需求';
GO
