-- 基础测试：验证夜间档数据
USE OperateData;
GO

-- 查看夜间档订单总数
SELECT 
    COUNT(*) AS 夜间档订单总数,
    SUM(TotalAmount) AS 夜间档总金额
FROM dbo.RmCloseInfo_Test rt
WHERE rt.ShopId = 3 
    AND rt.WorkDate = '20250509' 
    AND rt.OpenDateTime >= DATEADD(hour, 20, CAST('20250509' AS datetime));
GO

-- 查看有买断消费的订单
SELECT 
    COUNT(DISTINCT rt.InvNo) AS 买断订单数
FROM dbo.RmCloseInfo_Test rt
JOIN dbo.FdCashBak fcb ON rt.InvNo = fcb.InvNo
WHERE rt.ShopId = 3 
    AND rt.WorkDate = '20250509' 
    AND rt.OpenDateTime >= DATEADD(hour, 20, CAST('20250509' AS datetime))
    AND fcb.FdCName LIKE '%买断%';
GO

-- 查看有畅饮消费的订单
SELECT 
    COUNT(DISTINCT rt.InvNo) AS 畅饮订单数
FROM dbo.RmCloseInfo_Test rt
JOIN dbo.FdCashBak fcb ON rt.InvNo = fcb.InvNo
WHERE rt.ShopId = 3 
    AND rt.WorkDate = '20250509' 
    AND rt.OpenDateTime >= DATEADD(hour, 20, CAST('20250509' AS datetime))
    AND fcb.FdCName LIKE '%畅饮%';
GO
