{"total_orders": 11, "checked_out": 10, "real_direct_fall": 2, "single_slot": 8, "analysis_results": [{"InvNo": "A02426864", "CustName": "梁女士", "ComeDate": "20250717", "ComeTime": "19:28:06", "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Numbers": 3, "RmNo": "801", "Remark": "肆位，其中一位半价", "预约状态": "预约单时间段 (19:00-22:00)", "预约直落可行性": false, "CloseDatetime": "2025-07-17 21:36:24.257", "Tot": 568, "实际消费时长": "2.1小时", "消费分钟数": 128, "时长判断": "❌ 时长不足 (需≥3.0h)", "真实直落状态": "单时间段消费", "时间段类型": "TimeType=12", "是否夜场": false}, {"InvNo": "A02426880", "CustName": "林先生", "ComeDate": "20250717", "ComeTime": "22:00:56", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "831", "Remark": "5位，现买", "预约状态": "预约单时间段 (20:00)", "预约直落可行性": false, "CloseDatetime": "2025-07-18 01:55:13.710", "Tot": 1592, "实际消费时长": "3.9小时", "消费分钟数": 234, "时长判断": "✅ 符合直落时长", "真实直落状态": "✅ 真正直落", "时间段类型": "TimeType=126", "是否夜场": true}, {"InvNo": "A02426898", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:48:16", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 5, "RmNo": "309", "Remark": "陆位（其中一位儿童）V返", "预约状态": "预约单时间段 (11:50-14:50)", "预约直落可行性": false, "CloseDatetime": "2025-07-18 14:10:51.393", "Tot": 894, "实际消费时长": "2.4小时", "消费分钟数": 142, "时长判断": "❌ 时长不足 (需≥3.0h)", "真实直落状态": "单时间段消费", "时间段类型": "TimeType=12", "是否夜场": false}, {"InvNo": "A02426869", "CustName": "贵宾小姐女士女士", "ComeDate": "20250717", "ComeTime": "20:56:23", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "555", "Remark": "6位，中房畅饮", "预约状态": "预约单时间段 (20:00)", "预约直落可行性": false, "CloseDatetime": "2025-07-18 02:05:39.290", "Tot": 3543, "实际消费时长": "5.2小时", "消费分钟数": 309, "时长判断": "✅ 符合直落时长", "真实直落状态": "✅ 真正直落", "时间段类型": "TimeType=126", "是否夜场": true}, {"InvNo": "A02426914", "CustName": "贵宾先生", "ComeDate": "20250718", "ComeTime": "14:02:31", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "666", "Remark": "", "预约状态": "预约单时间段 (13:30-16:30)", "预约直落可行性": false, "CloseDatetime": "未结账", "Tot": null, "实际消费时长": "未结账", "真实直落状态": "未结账", "时长判断": "未结账"}, {"InvNo": "A02426911", "CustName": "段女士", "ComeDate": "20250718", "ComeTime": "13:24:23", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 10, "RmNo": "333", "Remark": "拾位美团1488X1", "预约状态": "预约单时间段 (13:30-16:30)", "预约直落可行性": false, "CloseDatetime": "2025-07-18 15:36:42.427", "Tot": 0, "实际消费时长": "2.2小时", "消费分钟数": 132, "时长判断": "❌ 时长不足 (需≥3.0h)", "真实直落状态": "单时间段消费", "时间段类型": "TimeType=2", "是否夜场": false}, {"InvNo": "A02426816", "CustName": "张女士", "ComeDate": "20250717", "ComeTime": "13:19:49", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "313", "Remark": "贰位 V返卡扣", "预约状态": "预约单时间段 (13:30-16:30)", "预约直落可行性": false, "CloseDatetime": "2025-07-17 16:06:40.120", "Tot": 152, "实际消费时长": "2.8小时", "消费分钟数": 166, "时长判断": "❌ 时长不足 (需≥3.0h)", "真实直落状态": "单时间段消费", "时间段类型": "TimeType=2", "是否夜场": false}, {"InvNo": "A02426904", "CustName": "贵宾女士", "ComeDate": "20250718", "ComeTime": "13:20:02", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "302", "Remark": "贰位在线团购x2", "预约状态": "预约单时间段 (13:30-16:30)", "预约直落可行性": false, "CloseDatetime": "2025-07-18 15:36:51.507", "Tot": 20, "实际消费时长": "2.3小时", "消费分钟数": 136, "时长判断": "❌ 时长不足 (需≥3.0h)", "真实直落状态": "单时间段消费", "时间段类型": "TimeType=2", "是否夜场": false}, {"InvNo": "A02426908", "CustName": "刘宇轩先生", "ComeDate": "20250718", "ComeTime": "13:21:50", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 5, "RmNo": "806", "Remark": "提前购券，同意使用", "预约状态": "预约单时间段 (13:30-16:30)", "预约直落可行性": false, "CloseDatetime": "2025-07-18 15:35:25.970", "Tot": 0, "实际消费时长": "2.2小时", "消费分钟数": 133, "时长判断": "❌ 时长不足 (需≥3.0h)", "真实直落状态": "单时间段消费", "时间段类型": "TimeType=2", "是否夜场": false}, {"InvNo": "A02426850", "CustName": "吴先生", "ComeDate": "20250717", "ComeTime": "17:52:59", "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Numbers": 2, "RmNo": "806", "Remark": "贰位，V返", "预约状态": "预约单时间段 (18:00-21:00)", "预约直落可行性": false, "CloseDatetime": "2025-07-17 20:18:57.193", "Tot": 346, "实际消费时长": "2.4小时", "消费分钟数": 145, "时长判断": "❌ 时长不足 (需≥3.0h)", "真实直落状态": "单时间段消费", "时间段类型": "TimeType=1", "是否夜场": false}, {"InvNo": "A02426895", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:40:46", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 2, "RmNo": "319", "Remark": "寿星免一V返", "预约状态": "预约单时间段 (11:50-14:50)", "预约直落可行性": false, "CloseDatetime": "2025-07-18 14:10:45.107", "Tot": 183, "实际消费时长": "2.5小时", "消费分钟数": 149, "时长判断": "❌ 时长不足 (需≥3.0h)", "真实直落状态": "单时间段消费", "时间段类型": "TimeType=12", "是否夜场": false}]}