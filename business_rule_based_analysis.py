#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于业务规则的KTV直落分析
考虑timetype直落类型配置和3小时最小直落时长
"""

import pyodbc
import pandas as pd
from datetime import datetime, timedelta
import json

class BusinessRuleBasedAnalyzer:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
        # 从查询结果得到的时间段配置和直落类型
        self.time_slot_config = {
            '02': {'name': '13:30-16:30', 'timetype': 2, 'timemode': 1, 'daytype': 7},
            '05': {'name': '20:00', 'timetype': 126, 'timemode': 2, 'daytype': 7},
            '07': {'name': '15:00-18:00', 'timetype': 12, 'timemode': 1, 'daytype': 7},
            '14': {'name': '01:00', 'timetype': 6, 'timemode': 2, 'daytype': 7},
            '25': {'name': '17:00-20:00', 'timetype': 2, 'timemode': 1, 'daytype': 7},
            '28': {'name': '11:50-14:50', 'timetype': 12, 'timemode': 1, 'daytype': 7},
            '29': {'name': '18:10-21:10', 'timetype': 1, 'timemode': 1, 'daytype': 60},
            '37': {'name': '18:00-21:00', 'timetype': 1, 'timemode': 1, 'daytype': 12345},
            '39': {'name': '19:00-22:00', 'timetype': 12, 'timemode': 1, 'daytype': 12340},
            '46': {'name': '19:00-21:30', 'timetype': 12, 'timemode': 1, 'daytype': 56}
        }
        
        # 实际结账数据
        self.checkout_data = {
            'A02426904': {'close_time': '2025-07-18 15:36:51.507', 'tot': 20},
            'A02426911': {'close_time': '2025-07-18 15:36:42.427', 'tot': 0},
            'A02426908': {'close_time': '2025-07-18 15:35:25.970', 'tot': 0},
            'A02426898': {'close_time': '2025-07-18 14:10:51.393', 'tot': 894},
            'A02426895': {'close_time': '2025-07-18 14:10:45.107', 'tot': 183},
            'A02426869': {'close_time': '2025-07-18 02:05:39.290', 'tot': 3543},
            'A02426880': {'close_time': '2025-07-18 01:55:13.710', 'tot': 1592},
            'A02426864': {'close_time': '2025-07-17 21:36:24.257', 'tot': 568},
            'A02426850': {'close_time': '2025-07-17 20:18:57.193', 'tot': 346},
            'A02426816': {'close_time': '2025-07-17 16:06:40.120', 'tot': 152}
        }
        
        # 开台数据
        self.open_data = {
            'A02426864': {
                'CustName': '梁女士', 'ComeDate': '20250717', 'ComeTime': '19:28:06',
                'Beg_Name': '19:00-22:00', 'End_Name': '19:00-22:00', 'Numbers': 3,
                'RmNo': '801', 'Remark': '肆位，其中一位半价'
            },
            'A02426880': {
                'CustName': '林先生', 'ComeDate': '20250717', 'ComeTime': '22:00:56',
                'Beg_Name': '20:00', 'End_Name': '20:00', 'Numbers': 2,
                'RmNo': '831', 'Remark': '5位，现买'
            },
            'A02426898': {
                'CustName': '李女士', 'ComeDate': '20250718', 'ComeTime': '11:48:16',
                'Beg_Name': '11:50-14:50', 'End_Name': '11:50-14:50', 'Numbers': 5,
                'RmNo': '309', 'Remark': '陆位（其中一位儿童）V返'
            },
            'A02426869': {
                'CustName': '贵宾小姐女士女士', 'ComeDate': '20250717', 'ComeTime': '20:56:23',
                'Beg_Name': '20:00', 'End_Name': '20:00', 'Numbers': 2,
                'RmNo': '555', 'Remark': '6位，中房畅饮'
            },
            'A02426914': {
                'CustName': '贵宾先生', 'ComeDate': '20250718', 'ComeTime': '14:02:31',
                'Beg_Name': '13:30-16:30', 'End_Name': '13:30-16:30', 'Numbers': 2,
                'RmNo': '666', 'Remark': ''
            },
            'A02426911': {
                'CustName': '段女士', 'ComeDate': '20250718', 'ComeTime': '13:24:23',
                'Beg_Name': '13:30-16:30', 'End_Name': '13:30-16:30', 'Numbers': 10,
                'RmNo': '333', 'Remark': '拾位美团1488X1'
            },
            'A02426816': {
                'CustName': '张女士', 'ComeDate': '20250717', 'ComeTime': '13:19:49',
                'Beg_Name': '13:30-16:30', 'End_Name': '13:30-16:30', 'Numbers': 2,
                'RmNo': '313', 'Remark': '贰位 V返卡扣'
            },
            'A02426904': {
                'CustName': '贵宾女士', 'ComeDate': '20250718', 'ComeTime': '13:20:02',
                'Beg_Name': '13:30-16:30', 'End_Name': '13:30-16:30', 'Numbers': 2,
                'RmNo': '302', 'Remark': '贰位在线团购x2'
            },
            'A02426908': {
                'CustName': '刘宇轩先生', 'ComeDate': '20250718', 'ComeTime': '13:21:50',
                'Beg_Name': '13:30-16:30', 'End_Name': '13:30-16:30', 'Numbers': 5,
                'RmNo': '806', 'Remark': '提前购券，同意使用'
            },
            'A02426850': {
                'CustName': '吴先生', 'ComeDate': '20250717', 'ComeTime': '17:52:59',
                'Beg_Name': '18:00-21:00', 'End_Name': '18:00-21:00', 'Numbers': 2,
                'RmNo': '806', 'Remark': '贰位，V返'
            },
            'A02426895': {
                'CustName': '李女士', 'ComeDate': '20250718', 'ComeTime': '11:40:46',
                'Beg_Name': '11:50-14:50', 'End_Name': '11:50-14:50', 'Numbers': 2,
                'RmNo': '319', 'Remark': '寿星免一V返'
            }
        }
    
    def get_time_slot_by_name(self, slot_name):
        """根据时间段名称获取时间段配置"""
        for slot_no, config in self.time_slot_config.items():
            if config['name'] == slot_name:
                return slot_no, config
        return None, None
    
    def can_direct_fall(self, from_slot, to_slot):
        """判断两个时间段是否可以直落"""
        from_slot_no, from_config = self.get_time_slot_by_name(from_slot)
        to_slot_no, to_config = self.get_time_slot_by_name(to_slot)
        
        if not from_config or not to_config:
            return False
        
        # 检查timetype是否有重叠（可以直落）
        from_timetype = from_config['timetype']
        to_timetype = to_config['timetype']
        
        # 如果两个时间段的timetype有公共因子，则可以直落
        # 这里简化处理，实际业务规则可能更复杂
        return from_timetype == to_timetype or (from_timetype & to_timetype) > 0
    
    def is_night_session(self, slot_name):
        """判断是否是夜场时间段（20:00之后）"""
        return '20:00' in slot_name or '01:00' in slot_name
    
    def analyze_with_business_rules(self):
        """基于业务规则分析直落情况"""
        print(f"\n{'='*80}")
        print(f"基于业务规则的KTV直落分析")
        print(f"样本订单: {len(self.open_data)} 个")
        print(f"{'='*80}")
        
        # 显示时间段配置
        print(f"\n📊 时间段配置和直落类型:")
        print(f"{'='*80}")
        print(f"{'时间段':<15} {'TimeType':<10} {'TimeMode':<10} {'DayType':<10} {'备注'}")
        print("-" * 80)
        
        for slot_no, config in sorted(self.time_slot_config.items()):
            note = "夜场" if self.is_night_session(config['name']) else "日场"
            print(f"{config['name']:<15} {config['timetype']:<10} {config['timemode']:<10} {config['daytype']:<10} {note}")
        
        analysis_results = []
        
        for invno, open_info in self.open_data.items():
            result = {
                'InvNo': invno,
                'CustName': open_info['CustName'],
                'ComeDate': open_info['ComeDate'],
                'ComeTime': open_info['ComeTime'],
                'Beg_Name': open_info['Beg_Name'],
                'End_Name': open_info['End_Name'],
                'Numbers': open_info['Numbers'],
                'RmNo': open_info['RmNo'],
                'Remark': open_info['Remark']
            }
            
            # 预约状态
            if open_info['Beg_Name'] != open_info['End_Name']:
                result['预约状态'] = f"预约直落 ({open_info['Beg_Name']} → {open_info['End_Name']})"
                result['预约直落可行性'] = self.can_direct_fall(open_info['Beg_Name'], open_info['End_Name'])
            else:
                result['预约状态'] = f"预约单时间段 ({open_info['Beg_Name']})"
                result['预约直落可行性'] = False
            
            # 实际消费分析
            if invno in self.checkout_data:
                checkout_info = self.checkout_data[invno]
                result['CloseDatetime'] = checkout_info['close_time']
                result['Tot'] = checkout_info['tot']
                
                try:
                    # 计算实际消费时长
                    come_datetime = datetime.strptime(f"{open_info['ComeDate']} {open_info['ComeTime']}", '%Y%m%d %H:%M:%S')
                    close_datetime = datetime.strptime(checkout_info['close_time'], '%Y-%m-%d %H:%M:%S.%f')
                    
                    duration = close_datetime - come_datetime
                    duration_hours = duration.total_seconds() / 3600
                    
                    result['实际消费时长'] = f"{duration_hours:.1f}小时"
                    result['消费分钟数'] = int(duration.total_seconds() / 60)
                    
                    # 业务规则判断
                    is_night = self.is_night_session(open_info['Beg_Name'])
                    min_direct_fall_hours = 2.5 if is_night else 3.0  # 夜场可能时间更灵活
                    
                    if duration_hours >= min_direct_fall_hours:
                        result['时长判断'] = '✅ 符合直落时长'
                        result['真实直落状态'] = '✅ 真正直落'
                    else:
                        result['时长判断'] = f'❌ 时长不足 (需≥{min_direct_fall_hours}h)'
                        result['真实直落状态'] = '单时间段消费'
                    
                    # 时间段类型分析
                    slot_no, slot_config = self.get_time_slot_by_name(open_info['Beg_Name'])
                    if slot_config:
                        result['时间段类型'] = f"TimeType={slot_config['timetype']}"
                        result['是否夜场'] = is_night
                    
                except Exception as e:
                    result['实际消费时长'] = f"计算错误: {e}"
                    result['真实直落状态'] = '无法判断'
                    result['时长判断'] = '计算错误'
            else:
                result['CloseDatetime'] = '未结账'
                result['Tot'] = None
                result['实际消费时长'] = '未结账'
                result['真实直落状态'] = '未结账'
                result['时长判断'] = '未结账'
            
            analysis_results.append(result)
        
        # 统计分析
        total_orders = len(analysis_results)
        checked_out = len([r for r in analysis_results if r['真实直落状态'] not in ['未结账', '无法判断']])
        real_direct_fall = len([r for r in analysis_results if r['真实直落状态'] == '✅ 真正直落'])
        single_slot = len([r for r in analysis_results if r['真实直落状态'] == '单时间段消费'])
        
        print(f"\n📈 分析结果统计:")
        print(f"  总订单数: {total_orders}")
        print(f"  已结账订单: {checked_out}")
        print(f"  真正直落订单: {real_direct_fall}")
        print(f"  单时间段消费: {single_slot}")
        print(f"  真实直落率: {real_direct_fall/checked_out*100:.1f}%" if checked_out > 0 else "  真实直落率: N/A")
        
        # 详细分析
        print(f"\n🔍 详细分析:")
        print(f"{'='*150}")
        print(f"{'订单号':<12} {'客户':<10} {'开台时间':<10} {'结账时间':<10} {'消费时长':<10} {'时长判断':<15} {'状态':<12} {'金额':<8}")
        print("-" * 150)
        
        for result in analysis_results:
            if result['CloseDatetime'] != '未结账':
                close_time = datetime.strptime(result['CloseDatetime'], '%Y-%m-%d %H:%M:%S.%f').strftime('%H:%M')
            else:
                close_time = '未结账'
            
            print(f"{result['InvNo']:<12} "
                  f"{result['CustName']:<10} "
                  f"{result['ComeTime'][:5]:<10} "
                  f"{close_time:<10} "
                  f"{result['实际消费时长']:<10} "
                  f"{result['时长判断']:<15} "
                  f"{result['真实直落状态']:<12} "
                  f"¥{result['Tot']:<7}" if result['Tot'] is not None else f"{'未结账':<8}")
        
        # 直落订单详情
        direct_fall_orders = [r for r in analysis_results if r['真实直落状态'] == '✅ 真正直落']
        if direct_fall_orders:
            print(f"\n✅ 真正直落订单详情:")
            print(f"{'='*80}")
            for order in direct_fall_orders:
                print(f"\n【{order['InvNo']} - {order['CustName']}】")
                print(f"  开台时间: {order['ComeTime']}")
                print(f"  结账时间: {order['CloseDatetime']}")
                print(f"  消费时长: {order['实际消费时长']}")
                print(f"  预约情况: {order['预约状态']}")
                print(f"  时间段类型: {order.get('时间段类型', 'N/A')}")
                print(f"  是否夜场: {'是' if order.get('是否夜场', False) else '否'}")
                print(f"  金额: ¥{order['Tot']}")
                print(f"  备注: {order['Remark']}")
        
        # 非直落订单分析
        non_direct_fall = [r for r in analysis_results if r['真实直落状态'] == '单时间段消费']
        if non_direct_fall:
            print(f"\n❌ 单时间段消费订单:")
            print(f"{'='*80}")
            for order in non_direct_fall:
                print(f"【{order['InvNo']} - {order['CustName']}】")
                print(f"  消费时长: {order['实际消费时长']}")
                print(f"  时长判断: {order['时长判断']}")
                print(f"  金额: ¥{order['Tot']}")
        
        return {
            'total_orders': total_orders,
            'checked_out': checked_out,
            'real_direct_fall': real_direct_fall,
            'single_slot': single_slot,
            'analysis_results': analysis_results
        }

def main():
    analyzer = BusinessRuleBasedAnalyzer()
    
    try:
        # 基于业务规则的分析
        result = analyzer.analyze_with_business_rules()
        
        # 保存结果
        if result:
            with open('business_rule_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n✅ 分析完成！")
            print(f"📄 详细结果已保存到: business_rule_analysis.json")
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
