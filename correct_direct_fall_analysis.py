#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的KTV直落订单分析脚本
基于Beg_Name != End_Name的准确识别逻辑
"""

import pyodbc
import pandas as pd
from datetime import datetime
import json

class CorrectDirectFallAnalyzer:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
    def get_connection(self, server, database, username, password):
        """获取数据库连接"""
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER={server};
        DATABASE={database};
        UID={username};
        PWD={password};
        """
        return pyodbc.connect(conn_str)
    
    def get_complete_data(self, work_date):
        """获取完整的开台和结账数据"""
        try:
            # 获取开台数据
            conn = self.get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
            
            open_query = f"""
            SELECT 
                Ikey, BookNo, ShopId, CustName, CustTel, ComeDate, ComeTime,
                Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName,
                RmNo, Invno, Remark
            FROM opencacheinfo
            WHERE shopid = {self.shop_id} AND ComeDate = '{work_date}'
            ORDER BY ComeTime
            """
            
            open_df = pd.read_sql(open_query, conn)
            conn.close()
            
            # 获取结账数据
            conn = self.get_connection('192.168.2.5', 'operatedata', 'sa', 'Musicbox123')
            
            close_query = f"""
            SELECT Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
            FROM rmcloseinfo 
            WHERE shopid = {self.shop_id} AND WorkDate = '{work_date}'
            ORDER BY CloseDatetime
            """
            
            close_df = pd.read_sql(close_query, conn)
            conn.close()
            
            # 关联数据
            merged_df = pd.merge(open_df, close_df, left_on='Invno', right_on='InvNo', how='left')
            
            return merged_df
            
        except Exception as e:
            print(f"获取数据失败: {e}")
            return pd.DataFrame()
    
    def identify_channel(self, vesa_name, remark):
        """识别消费渠道"""
        # 先检查备注
        if pd.notna(remark):
            remark_str = str(remark).lower()
            if '美团' in remark_str:
                return '美团'
            elif '抖音' in remark_str:
                return '抖音'
            elif '特权' in remark_str:
                return '特权预约'
        
        # 再检查支付方式
        if pd.notna(vesa_name):
            vesa_str = str(vesa_name).lower()
            if '美团' in vesa_str:
                return '美团'
            elif '抖音' in vesa_str:
                return '抖音'
            elif '特权' in vesa_str:
                return '特权预约'
        
        return 'K+'
    
    def analyze_business_data(self, work_date):
        """完整的业务数据分析"""
        print(f"\n{'='*80}")
        print(f"KTV业务数据分析报告 - {work_date}")
        print(f"门店: 名堂店 (ID: {self.shop_id})")
        print(f"{'='*80}")
        
        # 获取数据
        df = self.get_complete_data(work_date)
        
        if df.empty:
            print("没有数据")
            return {}
        
        # 添加渠道信息
        df['Channel'] = df.apply(lambda row: self.identify_channel(row['VesaName'], row['Remark']), axis=1)
        
        # 基本统计
        total_orders = len(df)
        paid_orders = len(df[df['Tot'].notna()])
        total_revenue = df['Tot'].sum()
        
        print(f"\n📊 基本数据统计")
        print(f"  总开台数: {total_orders}")
        print(f"  已结账数: {paid_orders}")
        print(f"  总营业额: ¥{total_revenue:,.0f}")
        
        # 正确识别直落订单
        direct_fall_df = df[(df['Beg_Name'] != df['End_Name']) & 
                           (df['Beg_Name'].notna()) & 
                           (df['End_Name'].notna())].copy()
        
        single_slot_df = df[(df['Beg_Name'] == df['End_Name']) & 
                           (df['Beg_Name'].notna())].copy()
        
        print(f"\n🎯 直落订单分析")
        print(f"  直落订单数: {len(direct_fall_df)}")
        print(f"  单时间段订单: {len(single_slot_df)}")
        print(f"  直落订单占比: {len(direct_fall_df)/total_orders*100:.1f}%")
        
        if not direct_fall_df.empty:
            direct_fall_revenue = direct_fall_df['Tot'].sum()
            print(f"  直落订单营业额: ¥{direct_fall_revenue:,.0f}")
            print(f"  直落营业额占比: {direct_fall_revenue/total_revenue*100:.1f}%")
        
        # 渠道分析
        print(f"\n📱 渠道分析")
        channel_stats = df.groupby('Channel').agg({
            'Invno': 'count',
            'Tot': ['sum', 'mean']
        }).round(0)
        
        for channel in channel_stats.index:
            count = channel_stats.loc[channel, ('Invno', 'count')]
            revenue = channel_stats.loc[channel, ('Tot', 'sum')]
            avg_revenue = channel_stats.loc[channel, ('Tot', 'mean')]
            
            print(f"  {channel}: {count}单, ¥{revenue:,.0f} (平均¥{avg_revenue:.0f})")
        
        # 直落订单详情
        if not direct_fall_df.empty:
            print(f"\n🔥 直落订单详情")
            print(f"{'='*80}")
            
            for _, row in direct_fall_df.iterrows():
                print(f"\n【{row['InvNo']} - {row['CustName']}】")
                print(f"  开台时间: {row['ComeTime']}")
                print(f"  时间段: {row['Beg_Name']} → {row['End_Name']}")
                print(f"  房间: {row['RmNo']} | 人数: {row['Numbers']}")
                print(f"  渠道: {row['Channel']}")
                if pd.notna(row['Tot']):
                    print(f"  金额: ¥{row['Tot']:.0f}")
                    print(f"  结账时间: {row['CloseDatetime']}")
                else:
                    print(f"  状态: 未结账")
                
                if pd.notna(row['Remark']):
                    print(f"  备注: {row['Remark']}")
        
        # 时间段分析
        print(f"\n⏰ 时间段分析")
        print(f"{'='*80}")
        
        # 统计各时间段的订单
        time_slot_stats = {}
        
        # 收集所有时间段
        all_slots = set()
        all_slots.update(df['Beg_Name'].dropna())
        all_slots.update(df['End_Name'].dropna())
        
        for slot in sorted(all_slots):
            # 在此时间段开始的订单
            start_in_slot = df[df['Beg_Name'] == slot]
            # 直落到此时间段的订单
            fall_to_slot = df[(df['End_Name'] == slot) & (df['Beg_Name'] != slot)]
            
            slot_revenue = start_in_slot['Tot'].sum() + fall_to_slot['Tot'].sum()
            slot_orders = len(start_in_slot) + len(fall_to_slot)
            
            if slot_orders > 0:
                time_slot_stats[slot] = {
                    'start_orders': len(start_in_slot),
                    'direct_fall_in': len(fall_to_slot),
                    'total_orders': slot_orders,
                    'revenue': slot_revenue
                }
                
                print(f"{slot:<20} | 开始:{len(start_in_slot):>2} | 直落进入:{len(fall_to_slot):>2} | 总计:{slot_orders:>2} | ¥{slot_revenue:>8,.0f}")
        
        # 生成分析结果
        analysis_result = {
            'work_date': work_date,
            'shop_id': self.shop_id,
            'summary': {
                'total_orders': total_orders,
                'paid_orders': paid_orders,
                'total_revenue': float(total_revenue),
                'direct_fall_orders': len(direct_fall_df),
                'direct_fall_revenue': float(direct_fall_df['Tot'].sum()) if not direct_fall_df.empty else 0,
                'single_slot_orders': len(single_slot_df)
            },
            'channel_analysis': {
                channel: {
                    'count': int(channel_stats.loc[channel, ('Invno', 'count')]),
                    'revenue': float(channel_stats.loc[channel, ('Tot', 'sum')]),
                    'avg_revenue': float(channel_stats.loc[channel, ('Tot', 'mean')])
                }
                for channel in channel_stats.index
            },
            'direct_fall_details': direct_fall_df[['InvNo', 'CustName', 'ComeTime', 'Beg_Name', 'End_Name', 'RmNo', 'Numbers', 'Tot', 'Channel', 'Remark']].to_dict('records'),
            'time_slot_analysis': time_slot_stats
        }
        
        return analysis_result
    
    def generate_sql_queries(self, work_date):
        """生成验证用的SQL查询"""
        print(f"\n📝 生成验证SQL查询")
        print(f"{'='*60}")
        
        # 直落订单查询
        direct_fall_sql = f"""
-- 查询{work_date}所有直落订单
SELECT 
    o.InvNo, o.CustName, o.ComeTime, 
    o.Beg_Name, o.End_Name, o.RmNo, o.Numbers,
    c.CloseDatetime, c.Tot, c.VesaName, o.Remark
FROM [193.112.2.229].rms2019.dbo.opencacheinfo o
LEFT JOIN [192.168.2.5].operatedata.dbo.rmcloseinfo c 
    ON o.Invno = c.InvNo AND o.shopid = c.shopid
WHERE o.shopid = {self.shop_id} 
    AND o.ComeDate = '{work_date}'
    AND o.Beg_Name != o.End_Name
    AND o.Beg_Name IS NOT NULL 
    AND o.End_Name IS NOT NULL
ORDER BY o.ComeTime;
"""
        
        # 时间段统计查询
        time_slot_sql = f"""
-- 查询{work_date}各时间段统计
WITH TimeSlotStats AS (
    SELECT 
        Beg_Name as TimeSlot,
        'Start' as Type,
        COUNT(*) as OrderCount,
        SUM(ISNULL(c.Tot, 0)) as Revenue
    FROM [193.112.2.229].rms2019.dbo.opencacheinfo o
    LEFT JOIN [192.168.2.5].operatedata.dbo.rmcloseinfo c 
        ON o.Invno = c.InvNo AND o.shopid = c.shopid
    WHERE o.shopid = {self.shop_id} AND o.ComeDate = '{work_date}'
        AND o.Beg_Name IS NOT NULL
    GROUP BY Beg_Name
    
    UNION ALL
    
    SELECT 
        End_Name as TimeSlot,
        'DirectFallIn' as Type,
        COUNT(*) as OrderCount,
        SUM(ISNULL(c.Tot, 0)) as Revenue
    FROM [193.112.2.229].rms2019.dbo.opencacheinfo o
    LEFT JOIN [192.168.2.5].operatedata.dbo.rmcloseinfo c 
        ON o.Invno = c.InvNo AND o.shopid = c.shopid
    WHERE o.shopid = {self.shop_id} AND o.ComeDate = '{work_date}'
        AND o.End_Name IS NOT NULL AND o.Beg_Name != o.End_Name
    GROUP BY End_Name
)
SELECT 
    TimeSlot,
    SUM(CASE WHEN Type = 'Start' THEN OrderCount ELSE 0 END) as StartOrders,
    SUM(CASE WHEN Type = 'DirectFallIn' THEN OrderCount ELSE 0 END) as DirectFallIn,
    SUM(OrderCount) as TotalOrders,
    SUM(Revenue) as TotalRevenue
FROM TimeSlotStats
GROUP BY TimeSlot
ORDER BY TimeSlot;
"""
        
        # 保存SQL文件
        with open(f'direct_fall_verification_{work_date}.sql', 'w', encoding='utf-8') as f:
            f.write(f"-- KTV直落订单验证查询 - {work_date}\n")
            f.write(f"-- 门店: 名堂店 (ID: {self.shop_id})\n\n")
            f.write(direct_fall_sql)
            f.write("\n\n")
            f.write(time_slot_sql)
        
        print(f"SQL查询已保存到: direct_fall_verification_{work_date}.sql")

def main():
    analyzer = CorrectDirectFallAnalyzer()
    
    work_date = '20250717'
    
    try:
        # 完整业务分析
        result = analyzer.analyze_business_data(work_date)
        
        # 生成验证SQL
        analyzer.generate_sql_queries(work_date)
        
        # 保存分析结果
        if result:
            with open(f'correct_analysis_{work_date}.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n✅ 分析完成！")
            print(f"📄 详细结果已保存到: correct_analysis_{work_date}.json")
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
