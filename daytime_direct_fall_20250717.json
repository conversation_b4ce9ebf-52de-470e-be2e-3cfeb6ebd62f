{"total_orders": 72, "daytime_count": 54, "night_count": 18, "real_direct_fall": 0, "analysis_results": [{"InvNo": "A02426892", "CustName": "黄先生", "ComeTime": "00:53:50", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "319", "Remark": "两位\n卡扣返还到三点", "CloseDatetime": "2025-07-18 02:01:11.377000", "Tot": 621, "进场类型": "白天档", "预约状态": "预约单时间段 (20:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "25.1小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426806", "CustName": "张女士", "ComeTime": "11:41:10", "Beg_Name": "11:50-14:50", "End_Name": "13:30-16:30", "Numbers": 4, "RmNo": "308", "Remark": "14：50后按四位用餐直落至17：50", "CloseDatetime": "2025-07-17 14:47:49.303000", "Tot": 609, "进场类型": "白天档", "预约状态": "预约直落 (11:50-14:50 → 13:30-16:30)", "直落可行性": "❌ 不可直落", "TimeType组合": "12 → 2", "实际消费时长": "3.1小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426807", "CustName": "欧女士", "ComeTime": "11:41:21", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 4, "RmNo": "309", "Remark": "肆位\n抖音598", "CloseDatetime": "2025-07-17 14:46:47.870000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (11:50-14:50)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "3.1小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426808", "CustName": "林先生", "ComeTime": "11:41:44", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 4, "RmNo": "306", "Remark": "肆位\n美团468X1", "CloseDatetime": "2025-07-17 14:47:02.487000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (11:50-14:50)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "3.1小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426809", "CustName": "董文女士", "ComeTime": "11:41:49", "Beg_Name": "11:50-14:50", "End_Name": "13:30-16:30", "Numbers": 5, "RmNo": "311", "Remark": "14:50后伍位不用餐直落到17:50", "CloseDatetime": "2025-07-17 14:46:55.843000", "Tot": 863, "进场类型": "白天档", "预约状态": "预约直落 (11:50-14:50 → 13:30-16:30)", "直落可行性": "❌ 不可直落", "TimeType组合": "12 → 2", "实际消费时长": "3.1小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426810", "CustName": "郑女士", "ComeTime": "11:42:03", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 4, "RmNo": "315", "Remark": "伍位（其中壹位半价）\n美团468x1\nv：卡扣", "CloseDatetime": "2025-07-17 14:46:39.573000", "Tot": 80, "进场类型": "白天档", "预约状态": "预约单时间段 (11:50-14:50)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "3.1小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426811", "CustName": "罗女士", "ComeTime": "11:42:16", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 6, "RmNo": "312", "Remark": "柒位\n美团888\nV返", "CloseDatetime": "2025-07-17 14:46:30.180000", "Tot": 152, "进场类型": "白天档", "预约状态": "预约单时间段 (11:50-14:50)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "3.1小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426812", "CustName": "孙松荣先生", "ComeTime": "11:49:18", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 3, "RmNo": "305", "Remark": "叁位\n特权457", "CloseDatetime": "2025-07-17 14:47:44.047000", "Tot": 457, "进场类型": "白天档", "预约状态": "预约单时间段 (11:50-14:50)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "3.0小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426813", "CustName": "肖女士", "ComeTime": "12:03:45", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 3, "RmNo": "303", "Remark": "13760336732 不返\n叁位", "CloseDatetime": "2025-07-17 14:47:17.213000", "Tot": 457, "进场类型": "白天档", "预约状态": "预约单时间段 (11:50-14:50)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.7小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426814", "CustName": "田女士", "ComeTime": "12:04:13", "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Numbers": 2, "RmNo": "302", "Remark": "贰位\n美团 122×2", "CloseDatetime": "2025-07-17 14:47:08.790000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (11:50-14:50)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.7小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426816", "CustName": "张女士", "ComeTime": "13:19:49", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "313", "Remark": "贰位 V返卡扣 ", "CloseDatetime": "2025-07-17 16:06:40.120000", "Tot": 152, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.8小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426817", "CustName": "林先生", "ComeTime": "13:19:53", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "316", "Remark": "贰位\n美团148*2", "CloseDatetime": "2025-07-17 16:06:12.883000", "Tot": 20, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.8小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426818", "CustName": "梁女士", "ComeTime": "13:20:15", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 6, "RmNo": "318", "Remark": "陆位\n美团888", "CloseDatetime": "2025-07-17 16:06:19.060000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.8小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426819", "CustName": "周女士", "ComeTime": "13:20:41", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 4, "RmNo": "338", "Remark": "肆位\n美团 560×1", "CloseDatetime": "2025-07-17 16:06:05.007000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.8小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426820", "CustName": "贵宾女士", "ComeTime": "13:21:09", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "301", "Remark": "贰位\n在线预定x2\n16：30", "CloseDatetime": "2025-07-17 16:05:57.410000", "Tot": 20, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.7小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426821", "CustName": "贵宾先生", "ComeTime": "13:21:15", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 4, "RmNo": "BOS3", "Remark": "伍位 其中一位儿童优惠价 美预138X4\nV返\n", "CloseDatetime": "2025-07-17 16:06:25.690000", "Tot": 80, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.8小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426822", "CustName": "胡女士", "ComeTime": "13:24:07", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 4, "RmNo": "310", "Remark": "肆位 特权512 ", "CloseDatetime": "2025-07-17 16:06:47.813000", "Tot": 527, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.7小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426823", "CustName": "黄先生", "ComeTime": "13:26:30", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "808", "Remark": "贰位\n特权✖️2", "CloseDatetime": "2025-07-17 16:07:02.647000", "Tot": 284, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.7小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426824", "CustName": "邓女士", "ComeTime": "13:26:58", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 3, "RmNo": "806", "Remark": "叁位\n特权✖️3\n无会员", "CloseDatetime": "2025-07-17 16:06:54.817000", "Tot": 395, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.7小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426825", "CustName": "张女士", "ComeTime": "13:30:50", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 3, "RmNo": "805", "Remark": "参位\n特权X3\nV返", "CloseDatetime": "2025-07-17 16:07:09.573000", "Tot": 395, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.6小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426826", "CustName": "贵宾女士", "ComeTime": "13:40:25", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "803", "Remark": "贰位\n美预138✖️2", "CloseDatetime": "2025-07-17 16:06:33.397000", "Tot": 20, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.4小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426827", "CustName": "戴先生", "ComeTime": "14:06:16", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "809", "Remark": "贰位\nV返", "CloseDatetime": "2025-07-17 16:08:45.687000", "Tot": 284, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.0小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426828", "CustName": "辛女士", "ComeTime": "14:25:11", "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Numbers": 2, "RmNo": "333", "Remark": "345275052248（白金卡）", "CloseDatetime": "2025-07-17 21:48:27.193000", "Tot": 2482, "进场类型": "白天档", "预约状态": "预约单时间段 (13:30-16:30)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "7.4小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426830", "CustName": "贵宾女士", "ComeTime": "14:58:27", "Beg_Name": "15:00-18:00", "End_Name": "15:00-18:00", "Numbers": 4, "RmNo": "302", "Remark": "肆位\n在线预定x4\n18：00", "CloseDatetime": "2025-07-17 17:23:41.817000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (15:00-18:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.4小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426852", "CustName": "贵宾女士", "ComeTime": "14:58:50", "Beg_Name": "15:00-18:00", "End_Name": "17:00-20:00", "Numbers": 4, "RmNo": "312", "Remark": "18:00后陆位直落到21:0", "CloseDatetime": "2025-07-17 20:17:08.557000", "Tot": 216, "进场类型": "白天档", "预约状态": "预约直落 (15:00-18:00 → 17:00-20:00)", "直落可行性": "❌ 不可直落", "TimeType组合": "12 → 2", "实际消费时长": "5.3小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426832", "CustName": "胡萝卜??先生先生", "ComeTime": "14:59:26", "Beg_Name": "15:00-18:00", "End_Name": "15:00-18:00", "Numbers": 3, "RmNo": "303", "Remark": "叁位\n特权x3\n18：00", "CloseDatetime": "2025-07-17 17:23:33.393000", "Tot": 395, "进场类型": "白天档", "预约状态": "预约单时间段 (15:00-18:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.4小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426833", "CustName": "唐女士", "ComeTime": "15:00:39", "Beg_Name": "15:00-18:00", "End_Name": "15:00-18:00", "Numbers": 2, "RmNo": "305", "Remark": "贰位\nv返\n18：00", "CloseDatetime": "2025-07-17 17:23:16.437000", "Tot": 284, "进场类型": "白天档", "预约状态": "预约单时间段 (15:00-18:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.4小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426834", "CustName": "骆娇女士", "ComeTime": "15:05:24", "Beg_Name": "15:00-18:00", "End_Name": "15:00-18:00", "Numbers": 8, "RmNo": "315", "Remark": "拾位（其中四位半价）\n44010420160726****\n生日一位全免*1\nV卡扣，v返", "CloseDatetime": "2025-07-17 17:23:00.227000", "Tot": 980, "进场类型": "白天档", "预约状态": "预约单时间段 (15:00-18:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.3小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426835", "CustName": "周先生先生", "ComeTime": "15:06:07", "Beg_Name": "15:00-18:00", "End_Name": "15:00-18:00", "Numbers": 2, "RmNo": "306", "Remark": "贰位\n特权x2\n18：00", "CloseDatetime": "2025-07-17 17:23:25.437000", "Tot": 284, "进场类型": "白天档", "预约状态": "预约单时间段 (15:00-18:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.3小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426836", "CustName": "郭女士", "ComeTime": "15:07:53", "Beg_Name": "15:00-18:00", "End_Name": "15:00-18:00", "Numbers": 4, "RmNo": "309", "Remark": "肆位\n美团560", "CloseDatetime": "2025-07-17 17:22:39.743000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (15:00-18:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.2小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426837", "CustName": "简敏清女士", "ComeTime": "15:13:14", "Beg_Name": "15:00-18:00", "End_Name": "15:00-18:00", "Numbers": 3, "RmNo": "802", "Remark": "参位\nV返", "CloseDatetime": "2025-07-17 17:22:31.897000", "Tot": 395, "进场类型": "白天档", "预约状态": "预约单时间段 (15:00-18:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.2小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426838", "CustName": "罗先生", "ComeTime": "16:49:53", "Beg_Name": "17:00-20:00", "End_Name": "17:00-20:00", "Numbers": 3, "RmNo": "310", "Remark": "叁位\n在线团购x3", "CloseDatetime": "2025-07-17 19:22:06.917000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (17:00-20:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.5小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426839", "CustName": "周女士", "ComeTime": "16:49:58", "Beg_Name": "17:00-20:00", "End_Name": "17:00-20:00", "Numbers": 4, "RmNo": "316", "Remark": "未预约，提前购买给予使用", "CloseDatetime": "2025-07-17 19:21:56.477000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (17:00-20:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.5小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426866", "CustName": "染女士", "ComeTime": "17:08:07", "Beg_Name": "17:00-20:00", "End_Name": "19:00-22:00", "Numbers": 5, "RmNo": "318", "Remark": "20:00后五位用餐直落到23:00", "CloseDatetime": "2025-07-17 21:49:04.740000", "Tot": 463, "进场类型": "白天档", "预约状态": "预约直落 (17:00-20:00 → 19:00-22:00)", "直落可行性": "❌ 不可直落", "TimeType组合": "2 → 12", "实际消费时长": "4.7小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426841", "CustName": "陈女士", "ComeTime": "17:09:32", "Beg_Name": "17:00-20:00", "End_Name": "17:00-20:00", "Numbers": 4, "RmNo": "313", "Remark": "肆位\n美团520x1", "CloseDatetime": "2025-07-17 19:21:43.250000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (17:00-20:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.2小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426843", "CustName": "林先生", "ComeTime": "17:17:28", "Beg_Name": "17:00-20:00", "End_Name": "17:00-20:00", "Numbers": 7, "RmNo": "BOS3", "Remark": "柒位\n美团468x2，122x1", "CloseDatetime": "2025-07-17 19:22:15.183000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (17:00-20:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.1小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426865", "CustName": "郭女士", "ComeTime": "17:30:28", "Beg_Name": "17:00-20:00", "End_Name": "19:00-22:00", "Numbers": 5, "RmNo": "338", "Remark": "20：30后按伍位用餐直落至23：30", "CloseDatetime": "2025-07-18 01:58:38.070000", "Tot": 247, "进场类型": "白天档", "预约状态": "预约直落 (17:00-20:00 → 19:00-22:00)", "直落可行性": "❌ 不可直落", "TimeType组合": "2 → 12", "实际消费时长": "8.5小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426845", "CustName": "刘女士", "ComeTime": "17:39:10", "Beg_Name": "17:00-20:00", "End_Name": "17:00-20:00", "Numbers": 2, "RmNo": "319", "Remark": "贰位\nv：返", "CloseDatetime": "2025-07-17 19:21:16.343000", "Tot": 346, "进场类型": "白天档", "预约状态": "预约单时间段 (17:00-20:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "1.7小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426846", "CustName": "卓海燕女士", "ComeTime": "17:51:05", "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Numbers": 14, "RmNo": "809", "Remark": "拾肆位，毕业季10人X1    4人X1", "CloseDatetime": "2025-07-17 20:18:50.033000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (18:00-21:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.5小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426847", "CustName": "周小玉女士", "ComeTime": "17:51:40", "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Numbers": 2, "RmNo": "301", "Remark": "贰位\n特权✖️2", "CloseDatetime": "2025-07-17 20:19:05.320000", "Tot": 346, "进场类型": "白天档", "预约状态": "预约单时间段 (18:00-21:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.5小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426848", "CustName": "苏女士", "ComeTime": "17:52:16", "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Numbers": 9, "RmNo": "858", "Remark": "玖位，V返", "CloseDatetime": "2025-07-17 20:18:43.013000", "Tot": 1856, "进场类型": "白天档", "预约状态": "预约单时间段 (18:00-21:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.4小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426849", "CustName": "赖女士", "ComeTime": "17:52:29", "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Numbers": 7, "RmNo": "666", "Remark": "陆位，美团888\n实际消费人数6位，削单", "CloseDatetime": "2025-07-17 20:35:44.850000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (18:00-21:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.7小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426850", "CustName": "吴先生", "ComeTime": "17:52:59", "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Numbers": 2, "RmNo": "806", "Remark": "贰位，V返", "CloseDatetime": "2025-07-17 20:18:57.193000", "Tot": 346, "进场类型": "白天档", "预约状态": "预约单时间段 (18:00-21:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.4小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426851", "CustName": "蔡先生先生", "ComeTime": "17:58:13", "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Numbers": 2, "RmNo": "803", "Remark": "贰位\n美团248✖️1", "CloseDatetime": "2025-07-17 20:19:11.670000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (18:00-21:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.3小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426854", "CustName": "刘女士", "ComeTime": "18:49:48", "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Numbers": 6, "RmNo": "308", "Remark": "陆位\n美团888六人x1\n22：00", "CloseDatetime": "2025-07-17 21:37:06.390000", "Tot": 0, "进场类型": "白天档", "预约状态": "预约单时间段 (19:00-22:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.8小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426855", "CustName": "吕女士", "ComeTime": "18:49:57", "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Numbers": 5, "RmNo": "306", "Remark": "柒位（其中贰位半价）\n卡扣/v返\n美团468四人x1\n22：00", "CloseDatetime": "2025-07-17 21:36:34.847000", "Tot": 323, "进场类型": "白天档", "预约状态": "预约单时间段 (19:00-22:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.8小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426881", "CustName": "贵宾女士", "ComeTime": "18:54:01", "Beg_Name": "20:00", "End_Name": "01:00", "Numbers": 4, "RmNo": "305", "Remark": "到22点后人头直落肆位到1点", "CloseDatetime": "2025-07-18 01:55:39.327000", "Tot": 197, "进场类型": "白天档", "预约状态": "预约直落 (20:00 → 01:00)", "直落可行性": "❓ 时间段未找到", "TimeType组合": "N/A", "实际消费时长": "7.0小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426858", "CustName": "梅先生", "ComeTime": "18:58:09", "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Numbers": 7, "RmNo": "311", "Remark": "七位\n特权1139\n22:00", "CloseDatetime": "2025-07-17 21:36:59.793000", "Tot": 1139, "进场类型": "白天档", "预约状态": "预约单时间段 (19:00-22:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.6小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426859", "CustName": "朱先生", "ComeTime": "18:59:35", "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Numbers": 12, "RmNo": "309", "Remark": "拾贰位 美团 1488 X1 \nV 15913188900 不返", "CloseDatetime": "2025-07-17 21:36:07.253000", "Tot": 325, "进场类型": "白天档", "预约状态": "预约单时间段 (19:00-22:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.6小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426860", "CustName": "贵宾女士", "ComeTime": "19:03:48", "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Numbers": 2, "RmNo": "303", "Remark": "贰位\n在线预定x2\n22：00", "CloseDatetime": "2025-07-17 21:36:50.557000", "Tot": 20, "进场类型": "白天档", "预约状态": "预约单时间段 (19:00-22:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.6小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426861", "CustName": "贵宾女士女士", "ComeTime": "19:12:01", "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Numbers": 7, "RmNo": "315", "Remark": "九位\nv返", "CloseDatetime": "2025-07-17 21:36:18.343000", "Tot": 1464, "进场类型": "白天档", "预约状态": "预约单时间段 (19:00-22:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.4小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426862", "CustName": "严女士", "ComeTime": "19:13:51", "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Numbers": 4, "RmNo": "302", "Remark": "陆位（其中贰位半价），V：返", "CloseDatetime": "2025-07-17 21:38:50.617000", "Tot": 811, "进场类型": "白天档", "预约状态": "预约单时间段 (19:00-22:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.4小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426879", "CustName": "珍先生", "ComeTime": "19:25:53", "Beg_Name": "19:00-22:00", "End_Name": "20:00", "Numbers": 5, "RmNo": "802", "Remark": "伍位，美团四人姐妹X1\nV\n肆位用餐直落到一点", "CloseDatetime": "2025-07-18 01:57:46.637000", "Tot": 197, "进场类型": "白天档", "预约状态": "预约直落 (19:00-22:00 → 20:00)", "直落可行性": "❓ 时间段未找到", "TimeType组合": "N/A", "实际消费时长": "6.5小时", "时长判断": "✅ 符合直落时长", "最终判断": "❌ 预约不支持直落"}, {"InvNo": "A02426864", "CustName": "梁女士", "ComeTime": "19:28:06", "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Numbers": 3, "RmNo": "801", "Remark": "肆位，其中一位半价", "CloseDatetime": "2025-07-17 21:36:24.257000", "Tot": 568, "进场类型": "白天档", "预约状态": "预约单时间段 (19:00-22:00)", "直落可行性": "非直落预约", "TimeType组合": "N/A", "实际消费时长": "2.1小时", "时长判断": "❌ 时长不足 (需≥3.0h)", "最终判断": "单时间段消费"}, {"InvNo": "A02426869", "CustName": "贵宾小姐女士女士", "ComeTime": "20:56:23", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "555", "Remark": "6位，中房畅饮", "CloseDatetime": "2025-07-18 02:05:39.290000", "Tot": 3543, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "5.2小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426870", "CustName": "贵宾女士", "ComeTime": "21:00:34", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "826", "Remark": "五位，真大杯买断", "CloseDatetime": "2025-07-18 01:56:09.387000", "Tot": 1645, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "4.9小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426871", "CustName": "常先生", "ComeTime": "21:19:54", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "829", "Remark": "6位，买断套餐", "CloseDatetime": "2025-07-18 01:54:34.837000", "Tot": 1410, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "4.6小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426872", "CustName": "贵宾先生", "ComeTime": "21:21:33", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "830", "Remark": "自带小半瓶1L装洋酒，收300自带费，半瓶白酒收200自带费", "CloseDatetime": "2025-07-18 02:05:02.970000", "Tot": 3481, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "4.7小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426873", "CustName": "宝哥先生", "ComeTime": "21:23:05", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 4, "RmNo": "832", "Remark": "4位，买三到营业结束", "CloseDatetime": "2025-07-18 02:00:26.587000", "Tot": 647, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "4.6小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426874", "CustName": "杨总先生", "ComeTime": "21:24:08", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "828", "Remark": "8位\n会员卡返还\n买三到营业结束", "CloseDatetime": "2025-07-18 02:01:36.363000", "Tot": 1545, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "4.6小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426875", "CustName": "刘先生", "ComeTime": "21:25:10", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "825", "Remark": "4位，买三到结束\nV返", "CloseDatetime": "2025-07-18 01:55:32.543000", "Tot": 1009, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "4.5小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426876", "CustName": "生先生先生", "ComeTime": "21:40:47", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "999", "Remark": "5位 买到3:00 v：扣", "CloseDatetime": "2025-07-18 01:59:49.067000", "Tot": 1774, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "4.3小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426877", "CustName": "何先生", "ComeTime": "21:41:36", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 4, "RmNo": "666", "Remark": "4位\n买到一点钟\n卡扣房费", "CloseDatetime": "2025-07-18 02:01:52.900000", "Tot": 1844, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "4.3小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426878", "CustName": "吕先生先生", "ComeTime": "21:48:06", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "823", "Remark": "5位，买断", "CloseDatetime": "2025-07-18 01:55:53.087000", "Tot": 1060, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "4.1小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426880", "CustName": "林先生", "ComeTime": "22:00:56", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "831", "Remark": "5位，现买", "CloseDatetime": "2025-07-18 01:55:13.710000", "Tot": 1592, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "3.9小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426882", "CustName": "何先生", "ComeTime": "22:11:32", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 4, "RmNo": "313", "Remark": "四位\n畅饮\n会员返还", "CloseDatetime": "2025-07-18 02:05:26.183000", "Tot": 2047, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "3.9小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426883", "CustName": "温先生", "ComeTime": "22:15:16", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "805", "Remark": "捌位，百威支畅饮", "CloseDatetime": "2025-07-18 01:59:18.727000", "Tot": 2078, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "3.7小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426884", "CustName": "蔡先生", "ComeTime": "22:18:57", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "310", "Remark": "自由畅饮\n5位\n", "CloseDatetime": "2025-07-18 02:06:08.350000", "Tot": 2047, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "3.8小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426885", "CustName": "文女士", "ComeTime": "22:21:50", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "315", "Remark": "畅饮 拾位", "CloseDatetime": "2025-07-18 02:05:55.123000", "Tot": 2966, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "3.7小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426886", "CustName": "蔡先生", "ComeTime": "22:37:57", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 5, "RmNo": "808", "Remark": "柒位，自由畅饮", "CloseDatetime": "2025-07-18 01:54:57.970000", "Tot": 1743, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "3.3小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426887", "CustName": "贵宾先生", "ComeTime": "22:40:05", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 3, "RmNo": "311", "Remark": "中房现买到00：50  4位 ", "CloseDatetime": "2025-07-18 02:06:52.240000", "Tot": 795, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "3.4小时", "时长判断": "夜场无时长要求"}, {"InvNo": "A02426888", "CustName": "吴先生", "ComeTime": "22:48:47", "Beg_Name": "20:00", "End_Name": "20:00", "Numbers": 2, "RmNo": "312", "Remark": "10位，自由畅饮", "CloseDatetime": "2025-07-18 01:59:00.583000", "Tot": 2979, "进场类型": "夜场", "预约状态": "夜场预约 (20:00)", "直落可行性": "夜场无直落概念", "最终判断": "夜场消费", "TimeType组合": "N/A", "实际消费时长": "3.2小时", "时长判断": "夜场无时长要求"}]}