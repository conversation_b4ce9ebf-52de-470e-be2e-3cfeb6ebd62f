#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据库连接和查询
"""

import pyodbc
import pandas as pd

def test_connection():
    # 测试operatedata数据库连接
    try:
        conn_str = """
        DRIVER={ODBC Driver 17 for SQL Server};
        SERVER=192.168.2.5;
        DATABASE=operatedata;
        UID=sa;
        PWD=Musicbox123;
        """
        conn = pyodbc.connect(conn_str)
        
        # 简单查询测试
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM rmcloseinfo WHERE shopid = 11 AND WorkDate = '20250717'")
        count = cursor.fetchone()[0]
        print(f"结账记录总数: {count}")
        
        # 获取几条样本数据
        cursor.execute("""
        SELECT TOP 5 Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
        FROM rmcloseinfo 
        WHERE shopid = 11 AND WorkDate = '20250717'
        ORDER BY CloseDatetime DESC
        """)
        
        rows = cursor.fetchall()
        print("\n样本数据:")
        for row in rows:
            print(f"账单: {row[2]}, 时间: {row[4]}, 金额: {row[5]}, 渠道: {row[6]}")
        
        conn.close()
        
    except Exception as e:
        print(f"连接失败: {e}")

def test_pandas_query():
    try:
        conn_str = """
        DRIVER={ODBC Driver 17 for SQL Server};
        SERVER=192.168.2.5;
        DATABASE=operatedata;
        UID=sa;
        PWD=Musicbox123;
        """
        conn = pyodbc.connect(conn_str)
        
        query = """
        SELECT TOP 10 Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
        FROM rmcloseinfo 
        WHERE shopid = 11 AND WorkDate = '20250717'
        ORDER BY CloseDatetime DESC
        """
        
        df = pd.read_sql(query, conn)
        print(f"\nPandas查询结果: {len(df)} 条记录")
        print(df.head())
        
        conn.close()
        
    except Exception as e:
        print(f"Pandas查询失败: {e}")

if __name__ == "__main__":
    test_connection()
    test_pandas_query()
