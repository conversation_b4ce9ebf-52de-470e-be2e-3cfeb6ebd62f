-- 名堂店20250716直落订单验证SQL
-- 我的算法识别出以下订单为直落订单，请您验证是否准确

-- 第一步：查看我识别为直落的订单详情
SELECT 
    c.InvNo as '订单号',
    c.CloseDatetime as '结账时间',
    DATEADD(HOUR, -2.5, c.CloseDatetime) as '估算开台时间',
    CONVERT(varchar, DATEADD(HOUR, -2.5, c.CloseDatetime), 108) as '估算开台时分',
    CONVERT(varchar, c.CloseDatetime, 108) as '结账时分',
    c.Tot as '金额'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '20250716'
    AND c.InvNo IN (
        'A02426691', 'A02426700', 'A02426697', 'A02426698', 'A02426695', 'A02426696', 'A02426699',
        'A02426693', 'A02426694', 'A02426714', 'A02426705', 'A02426715', 'A02426711', 'A02426713',
        'A02426712', 'A02426710', 'A02426708', 'A02426707', 'A02426703', 'A02426709', 'A02426704',
        'A02426719', 'A02426725', 'A02426723', 'A02426717', 'A02426722', 'A02426720', 'A02426724',
        'A02426701', 'A02426721', 'A02426718', 'A02426730', 'A02426716', 'A02426727', 'A02426739',
        'A02426737', 'A02426734', 'A02426732', 'A02426733', 'A02426740', 'A02426738', 'A02426731',
        'A02426735', 'A02426742', 'A02426743', 'A02426736', 'A02426745', 'A02426728', 'A02426771',
        'A02426729', 'A02426744', 'A02426757', 'A02426759', 'A02426751', 'A02426758', 'A02426746',
        'A02426749', 'A02426760', 'A02426756', 'A02426750', 'A02426747', 'A02426754', 'A02426753',
        'A02426752', 'A02426748', 'A02426755', 'A02426772', 'A02426762', 'A02426768', 'A02426763',
        'A02426769', 'A02426770', 'A02426765', 'A02426764', 'A02426767', 'A02426766', 'A02426741',
        'A02426706'
    )
ORDER BY c.CloseDatetime;

-- 第二步：查看时间段配置
SELECT 
    t.TimeName as '时间段名称',
    FORMAT(t.BegTime/100, '00') + ':' + FORMAT(t.BegTime%100, '00') as '开始时间',
    FORMAT(t.EndTime/100, '00') + ':' + FORMAT(t.EndTime%100, '00') as '结束时间'
FROM shoptimeinfo st
LEFT JOIN timeinfo t ON st.timeno = t.timeno
WHERE st.shopid = 11
ORDER BY t.BegTime;

-- 第三步：验证逻辑 - 检查这些订单是否真的是直落
-- 直落定义：估算开台时间早于某个时间段开始，但结账时间在该时间段内或之后

-- 示例：11:50-14:50时间段的直落订单
SELECT 
    '11:50-14:50时间段' as '分析时间段',
    c.InvNo as '订单号',
    CONVERT(varchar, DATEADD(HOUR, -2.5, c.CloseDatetime), 108) as '估算开台时间',
    CONVERT(varchar, c.CloseDatetime, 108) as '结账时间',
    CASE 
        WHEN DATEADD(HOUR, -2.5, c.CloseDatetime) < CONVERT(datetime, '20250716 11:50:00') 
             AND c.CloseDatetime >= CONVERT(datetime, '20250716 11:50:00')
        THEN '是直落'
        ELSE '不是直落'
    END as '是否直落',
    c.Tot as '金额'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '20250716'
    AND c.InvNo IN (
        'A02426691', 'A02426700', 'A02426697', 'A02426698', 'A02426695', 'A02426696', 'A02426699'
    )
ORDER BY c.CloseDatetime;

-- 第四步：如果您有实际开台数据，可以对比验证
SELECT 
    o.BookNo as '开台号',
    o.CustName as '客户名称',
    o.ComeTime as '实际开台时间',
    o.RmNo as '房间号'
FROM opencacheinfo o
WHERE o.shopid = 11 
    AND o.ComeDate = '20250716'
    AND o.ComeTime BETWEEN '09:00:00' AND '15:00:00'  -- 查看可能对应的开台记录
ORDER BY o.ComeTime;
