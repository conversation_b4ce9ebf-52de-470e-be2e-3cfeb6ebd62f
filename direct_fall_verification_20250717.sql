-- KTV直落订单验证查询 - 20250717
-- 门店: 名堂店 (ID: 11)


-- 查询20250717所有直落订单
SELECT 
    o.InvNo, o.<PERSON>ust<PERSON><PERSON>, o.ComeTime, 
    o.Beg_Name, o.End_Name, o.RmNo, o.<PERSON>,
    c.<PERSON>, c.<PERSON>, c.<PERSON>, o.Remark
FROM [193.112.2.229].rms2019.dbo.opencacheinfo o
LEFT JOIN [192.168.2.5].operatedata.dbo.rmcloseinfo c 
    ON o.Invno = c.InvNo AND o.shopid = c.shopid
WHERE o.shopid = 11 
    AND o.ComeDate = '20250717'
    AND o.Beg_Name != o.End_Name
    AND o.Beg_Name IS NOT NULL 
    AND o.End_Name IS NOT NULL
ORDER BY o.ComeTime;



-- 查询20250717各时间段统计
WITH TimeSlotStats AS (
    SELECT 
        Beg_Name as TimeSlot,
        'Start' as Type,
        COUNT(*) as OrderCount,
        SUM(ISNULL(c<PERSON>, 0)) as Revenue
    FROM [193.112.2.229].rms2019.dbo.opencacheinfo o
    LEFT JOIN [192.168.2.5].operatedata.dbo.rmcloseinfo c 
        ON o.Invno = c.InvNo AND o.shopid = c.shopid
    WHERE o.shopid = 11 AND o.ComeDate = '20250717'
        AND o.Beg_Name IS NOT NULL
    GROUP BY Beg_Name
    
    UNION ALL
    
    SELECT 
        End_Name as TimeSlot,
        'DirectFallIn' as Type,
        COUNT(*) as OrderCount,
        SUM(ISNULL(c.Tot, 0)) as Revenue
    FROM [193.112.2.229].rms2019.dbo.opencacheinfo o
    LEFT JOIN [192.168.2.5].operatedata.dbo.rmcloseinfo c 
        ON o.Invno = c.InvNo AND o.shopid = c.shopid
    WHERE o.shopid = 11 AND o.ComeDate = '20250717'
        AND o.End_Name IS NOT NULL AND o.Beg_Name != o.End_Name
    GROUP BY End_Name
)
SELECT 
    TimeSlot,
    SUM(CASE WHEN Type = 'Start' THEN OrderCount ELSE 0 END) as StartOrders,
    SUM(CASE WHEN Type = 'DirectFallIn' THEN OrderCount ELSE 0 END) as DirectFallIn,
    SUM(OrderCount) as TotalOrders,
    SUM(Revenue) as TotalRevenue
FROM TimeSlotStats
GROUP BY TimeSlot
ORDER BY TimeSlot;
