-- 名堂店20250717直落订单验证SQL
-- 我的算法识别出以下订单为直落订单，请您验证是否准确

-- ===== 我识别的直落订单 =====

-- 1. 13:30-16:30时间段的直落订单（估算开台时间早于13:30，但结账时间在13:30之后）
SELECT 
    '13:30-16:30时间段直落' as '类型',
    c.InvNo as '订单号',
    CONVERT(varchar, DATEADD(HOUR, -2.5, c.CloseDatetime), 108) as '估算开台时间',
    CONVERT(varchar, c.CloseDatetime, 108) as '结账时间',
    c.Tot as '金额',
    '估算开台早于13:30，结账在13:30后' as '直落原因'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '20250717'
    AND c.InvNo IN (
        'A02426811', 'A02426810', 'A02426807', 'A02426809', 'A02426808', 
        'A02426814', 'A02426813', 'A02426812', 'A02426806'
    )
ORDER BY c.CloseDatetime;

-- 2. 15:00-18:00时间段的直落订单（估算开台时间早于15:00，但结账时间在15:00之后）
SELECT 
    '15:00-18:00时间段直落' as '类型',
    c.InvNo as '订单号',
    CONVERT(varchar, DATEADD(HOUR, -2.5, c.CloseDatetime), 108) as '估算开台时间',
    CONVERT(varchar, c.CloseDatetime, 108) as '结账时间',
    c.Tot as '金额',
    '估算开台早于15:00，结账在15:00后' as '直落原因'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '20250717'
    AND c.InvNo IN (
        'A02426820', 'A02426819', 'A02426817', 'A02426818', 'A02426821',
        'A02426826', 'A02426816', 'A02426822', 'A02426824', 'A02426823'
    )
ORDER BY c.CloseDatetime;

-- ===== 验证查询 =====

-- 3. 查看这些订单的详细信息
SELECT 
    c.InvNo as '订单号',
    c.CloseDatetime as '结账时间',
    DATEADD(HOUR, -2.5, c.CloseDatetime) as '估算开台时间',
    c.Tot as '金额',
    c.VesaName as '支付方式',
    DATEDIFF(MINUTE, DATEADD(HOUR, -2.5, c.CloseDatetime), c.CloseDatetime) as '估算消费分钟数'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '20250717'
    AND c.InvNo IN (
        'A02426811', 'A02426810', 'A02426807', 'A02426809', 'A02426808', 
        'A02426814', 'A02426813', 'A02426812', 'A02426806',
        'A02426820', 'A02426819', 'A02426817', 'A02426818', 'A02426821',
        'A02426826', 'A02426816', 'A02426822', 'A02426824', 'A02426823'
    )
ORDER BY c.CloseDatetime;

-- 4. 查看时间段配置
SELECT 
    t.TimeName as '时间段名称',
    FORMAT(t.BegTime/100, '00') + ':' + FORMAT(t.BegTime%100, '00') as '开始时间',
    FORMAT(t.EndTime/100, '00') + ':' + FORMAT(t.EndTime%100, '00') as '结束时间'
FROM shoptimeinfo st
LEFT JOIN timeinfo t ON st.timeno = t.timeno
WHERE st.shopid = 11
ORDER BY t.BegTime;

-- 5. 查看实际开台数据进行对比验证
SELECT 
    o.BookNo as '开台号',
    o.CustName as '客户名称',
    o.ComeTime as '实际开台时间',
    o.RmNo as '房间号'
FROM opencacheinfo o
WHERE o.shopid = 11 
    AND o.ComeDate = '20250717'
    AND o.ComeTime BETWEEN '12:00:00' AND '16:00:00'  -- 查看中午到下午的开台记录
ORDER BY o.ComeTime;

-- 6. 验证逻辑说明
/*
我的直落识别逻辑：
1. 估算开台时间 = 结账时间 - 2.5小时
2. 如果估算开台时间早于某个时间段的开始时间，但结账时间在该时间段内或之后，则认为是直落

例如：
- 订单A02426811：结账14:46:30，估算开台12:46:30
- 对于13:30-16:30时间段：12:46:30 < 13:30:00，且14:46:30 >= 13:30:00
- 因此判定为13:30-16:30时间段的直落订单

请您验证：
1. 这些订单的实际开台时间是否确实早于相应时间段？
2. 这些是真的直落还是补时？
3. 我的2.5小时估算是否合理？
*/
