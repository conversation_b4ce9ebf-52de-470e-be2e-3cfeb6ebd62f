{"total_orders": 60, "real_direct_fall": 60, "single_slot": 0, "analysis_results": [{"InvNo": "A02426895", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:40:46", "CloseDatetime": "2019-07-07 19:28:55.240000", "Tot": 0, "RmNo": "319", "Numbers": 2, "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Remark": "44010519900718xxxx\n贰位\n寿星免一✖️1\nV返", "实际消费时长": "-52864.2小时", "消费分钟数": -3171851, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (11:50-14:50)"}, {"InvNo": "A02426895", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:40:46", "CloseDatetime": "2021-01-24 01:12:47.537000", "Tot": 0, "RmNo": "319", "Numbers": 2, "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Remark": "44010519900718xxxx\n贰位\n寿星免一✖️1\nV返", "实际消费时长": "-39274.5小时", "消费分钟数": -2356467, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (11:50-14:50)"}, {"InvNo": "A02426895", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:40:46", "CloseDatetime": "2021-11-28 19:12:11.587000", "Tot": 166, "RmNo": "319", "Numbers": 2, "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Remark": "44010519900718xxxx\n贰位\n寿星免一✖️1\nV返", "实际消费时长": "-31864.5小时", "消费分钟数": -1911868, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (11:50-14:50)"}, {"InvNo": "A02426895", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:40:46", "CloseDatetime": "2022-02-10 19:11:41.170000", "Tot": 0, "RmNo": "319", "Numbers": 2, "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Remark": "44010519900718xxxx\n贰位\n寿星免一✖️1\nV返", "实际消费时长": "-30088.5小时", "消费分钟数": -1805309, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (11:50-14:50)"}, {"InvNo": "A02426895", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:40:46", "CloseDatetime": "2023-07-10 01:41:05.267000", "Tot": 183, "RmNo": "319", "Numbers": 2, "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Remark": "44010519900718xxxx\n贰位\n寿星免一✖️1\nV返", "实际消费时长": "-17746.0小时", "消费分钟数": -1064759, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (11:50-14:50)"}, {"InvNo": "A02426898", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:48:16", "CloseDatetime": "2019-07-07 19:07:03.500000", "Tot": 300, "RmNo": "309", "Numbers": 5, "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Remark": "陆位（其中一位儿童）\nV返", "实际消费时长": "-52864.7小时", "消费分钟数": -3171881, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (11:50-14:50)"}, {"InvNo": "A02426898", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:48:16", "CloseDatetime": "2021-01-24 01:09:28.533000", "Tot": 1272, "RmNo": "309", "Numbers": 5, "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Remark": "陆位（其中一位儿童）\nV返", "实际消费时长": "-39274.6小时", "消费分钟数": -2356478, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (11:50-14:50)"}, {"InvNo": "A02426898", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:48:16", "CloseDatetime": "2021-11-28 19:14:11.390000", "Tot": 85, "RmNo": "309", "Numbers": 5, "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Remark": "陆位（其中一位儿童）\nV返", "实际消费时长": "-31864.6小时", "消费分钟数": -1911874, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (11:50-14:50)"}, {"InvNo": "A02426898", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:48:16", "CloseDatetime": "2022-02-10 19:16:10.153000", "Tot": 170, "RmNo": "309", "Numbers": 5, "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Remark": "陆位（其中一位儿童）\nV返", "实际消费时长": "-30088.5小时", "消费分钟数": -1805312, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (11:50-14:50)"}, {"InvNo": "A02426898", "CustName": "李女士", "ComeDate": "20250718", "ComeTime": "11:48:16", "CloseDatetime": "2023-07-10 01:37:00.610000", "Tot": 1627, "RmNo": "309", "Numbers": 5, "Beg_Name": "11:50-14:50", "End_Name": "11:50-14:50", "Remark": "陆位（其中一位儿童）\nV返", "实际消费时长": "-17746.2小时", "消费分钟数": -1064771, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (11:50-14:50)"}, {"InvNo": "A02426816", "CustName": "张女士", "ComeDate": "20250717", "ComeTime": "13:19:49", "CloseDatetime": "2019-07-07 18:48:56.413000", "Tot": 210, "RmNo": "313", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位 V返卡扣 ", "实际消费时长": "-52842.5小时", "消费分钟数": -3170550, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"]], "跨越时间段数量": 8, "真实直落状态": "✅ 真正直落", "直落类型": "跨越8个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426816", "CustName": "张女士", "ComeDate": "20250717", "ComeTime": "13:19:49", "CloseDatetime": "2021-01-23 19:28:19.217000", "Tot": 0, "RmNo": "313", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位 V返卡扣 ", "实际消费时长": "-39257.9小时", "消费分钟数": -2355471, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426816", "CustName": "张女士", "ComeDate": "20250717", "ComeTime": "13:19:49", "CloseDatetime": "2021-11-28 15:24:31.020000", "Tot": 340, "RmNo": "313", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位 V返卡扣 ", "实际消费时长": "-31845.9小时", "消费分钟数": -1910755, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["28", "11:50-14:50"]], "跨越时间段数量": 5, "真实直落状态": "✅ 真正直落", "直落类型": "跨越5个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426816", "CustName": "张女士", "ComeDate": "20250717", "ComeTime": "13:19:49", "CloseDatetime": "2022-02-10 14:12:20.640000", "Tot": 340, "RmNo": "313", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位 V返卡扣 ", "实际消费时长": "-30071.1小时", "消费分钟数": -1804267, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["14", "01:00"], ["28", "11:50-14:50"]], "跨越时间段数量": 4, "真实直落状态": "✅ 真正直落", "直落类型": "跨越4个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426816", "CustName": "张女士", "ComeDate": "20250717", "ComeTime": "13:19:49", "CloseDatetime": "2023-07-09 19:33:35.643000", "Tot": 302, "RmNo": "313", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位 V返卡扣 ", "实际消费时长": "-17729.8小时", "消费分钟数": -1063786, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426816", "CustName": "张女士", "ComeDate": "20250717", "ComeTime": "13:19:49", "CloseDatetime": "2025-07-17 16:06:40.120000", "Tot": 152, "RmNo": "313", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位 V返卡扣 ", "实际消费时长": "2.8小时", "消费分钟数": 166, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["28", "11:50-14:50"]], "跨越时间段数量": 5, "真实直落状态": "✅ 真正直落", "直落类型": "跨越5个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426904", "CustName": "贵宾女士", "ComeDate": "20250718", "ComeTime": "13:20:02", "CloseDatetime": "2019-07-07 19:25:30.467000", "Tot": 0, "RmNo": "302", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位\n在线团购x2", "实际消费时长": "-52865.9小时", "消费分钟数": -3171954, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426904", "CustName": "贵宾女士", "ComeDate": "20250718", "ComeTime": "13:20:02", "CloseDatetime": "2021-01-24 01:10:51.493000", "Tot": 416, "RmNo": "302", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位\n在线团购x2", "实际消费时长": "-39276.2小时", "消费分钟数": -2356569, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426904", "CustName": "贵宾女士", "ComeDate": "20250718", "ComeTime": "13:20:02", "CloseDatetime": "2021-11-28 19:10:10.500000", "Tot": 170, "RmNo": "302", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位\n在线团购x2", "实际消费时长": "-31866.2小时", "消费分钟数": -1911969, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426904", "CustName": "贵宾女士", "ComeDate": "20250718", "ComeTime": "13:20:02", "CloseDatetime": "2022-02-10 19:11:03.073000", "Tot": 136, "RmNo": "302", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位\n在线团购x2", "实际消费时长": "-30090.1小时", "消费分钟数": -1805408, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426904", "CustName": "贵宾女士", "ComeDate": "20250718", "ComeTime": "13:20:02", "CloseDatetime": "2023-07-10 01:31:18.090000", "Tot": 873, "RmNo": "302", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "贰位\n在线团购x2", "实际消费时长": "-17747.8小时", "消费分钟数": -1064868, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426908", "CustName": "刘宇轩先生", "ComeDate": "20250718", "ComeTime": "13:21:50", "CloseDatetime": "2019-07-07 19:25:11.497000", "Tot": 0, "RmNo": "806", "Numbers": 5, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "提前购券，同意使用", "实际消费时长": "-52865.9小时", "消费分钟数": -3171956, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426908", "CustName": "刘宇轩先生", "ComeDate": "20250718", "ComeTime": "13:21:50", "CloseDatetime": "2021-01-24 01:14:51.657000", "Tot": 404, "RmNo": "806", "Numbers": 5, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "提前购券，同意使用", "实际消费时长": "-39276.1小时", "消费分钟数": -2356566, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426908", "CustName": "刘宇轩先生", "ComeDate": "20250718", "ComeTime": "13:21:50", "CloseDatetime": "2021-11-28 19:10:03.217000", "Tot": 0, "RmNo": "806", "Numbers": 5, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "提前购券，同意使用", "实际消费时长": "-31866.2小时", "消费分钟数": -1911971, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426908", "CustName": "刘宇轩先生", "ComeDate": "20250718", "ComeTime": "13:21:50", "CloseDatetime": "2022-02-10 21:58:11.547000", "Tot": 150, "RmNo": "806", "Numbers": 5, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "提前购券，同意使用", "实际消费时长": "-30087.4小时", "消费分钟数": -1805243, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426908", "CustName": "刘宇轩先生", "ComeDate": "20250718", "ComeTime": "13:21:50", "CloseDatetime": "2023-07-10 01:55:31.173000", "Tot": 1761, "RmNo": "806", "Numbers": 5, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "提前购券，同意使用", "实际消费时长": "-17747.4小时", "消费分钟数": -1064846, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426911", "CustName": "段女士", "ComeDate": "20250718", "ComeTime": "13:24:23", "CloseDatetime": "2019-07-07 19:28:14.270000", "Tot": 114, "RmNo": "333", "Numbers": 10, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "拾位\n美团1488X1", "实际消费时长": "-52865.9小时", "消费分钟数": -3171956, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426911", "CustName": "段女士", "ComeDate": "20250718", "ComeTime": "13:24:23", "CloseDatetime": "2021-01-24 01:25:11.753000", "Tot": 0, "RmNo": "333", "Numbers": 10, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "拾位\n美团1488X1", "实际消费时长": "-39276.0小时", "消费分钟数": -2356559, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426911", "CustName": "段女士", "ComeDate": "20250718", "ComeTime": "13:24:23", "CloseDatetime": "2021-11-28 18:52:26.210000", "Tot": 299, "RmNo": "333", "Numbers": 10, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "拾位\n美团1488X1", "实际消费时长": "-31866.5小时", "消费分钟数": -1911991, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"]], "跨越时间段数量": 8, "真实直落状态": "✅ 真正直落", "直落类型": "跨越8个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426911", "CustName": "段女士", "ComeDate": "20250718", "ComeTime": "13:24:23", "CloseDatetime": "2022-02-10 22:02:35.567000", "Tot": 245, "RmNo": "333", "Numbers": 10, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "拾位\n美团1488X1", "实际消费时长": "-30087.4小时", "消费分钟数": -1805241, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426911", "CustName": "段女士", "ComeDate": "20250718", "ComeTime": "13:24:23", "CloseDatetime": "2023-07-10 01:42:18.393000", "Tot": 914, "RmNo": "333", "Numbers": 10, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "拾位\n美团1488X1", "实际消费时长": "-17747.7小时", "消费分钟数": -1064862, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426914", "CustName": "贵宾先生", "ComeDate": "20250718", "ComeTime": "14:02:31", "CloseDatetime": "2019-07-07 19:12:43.283000", "Tot": 150, "RmNo": "666", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "拾伍位\n房费不用餐\n到17：00", "实际消费时长": "-52866.8小时", "消费分钟数": -3172009, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426914", "CustName": "贵宾先生", "ComeDate": "20250718", "ComeTime": "14:02:31", "CloseDatetime": "2021-01-23 22:21:46.273000", "Tot": 414, "RmNo": "666", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "拾伍位\n房费不用餐\n到17：00", "实际消费时长": "-39279.7小时", "消费分钟数": -2356780, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426914", "CustName": "贵宾先生", "ComeDate": "20250718", "ComeTime": "14:02:31", "CloseDatetime": "2021-11-28 18:47:30.673000", "Tot": 0, "RmNo": "666", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "拾伍位\n房费不用餐\n到17：00", "实际消费时长": "-31867.3小时", "消费分钟数": -1912035, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"]], "跨越时间段数量": 8, "真实直落状态": "✅ 真正直落", "直落类型": "跨越8个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426914", "CustName": "贵宾先生", "ComeDate": "20250718", "ComeTime": "14:02:31", "CloseDatetime": "2022-02-10 20:17:15.883000", "Tot": 225, "RmNo": "666", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "拾伍位\n房费不用餐\n到17：00", "实际消费时长": "-30089.8小时", "消费分钟数": -1805385, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426914", "CustName": "贵宾先生", "ComeDate": "20250718", "ComeTime": "14:02:31", "CloseDatetime": "2023-07-10 01:33:20.367000", "Tot": 276, "RmNo": "666", "Numbers": 2, "Beg_Name": "13:30-16:30", "End_Name": "13:30-16:30", "Remark": "拾伍位\n房费不用餐\n到17：00", "实际消费时长": "-17748.5小时", "消费分钟数": -1064909, "跨越时间段": [["02", "13:30-16:30"], ["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["28", "11:50-14:50"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 10, "真实直落状态": "✅ 真正直落", "直落类型": "跨越10个时间段", "预约状态": "预约单时间段 (13:30-16:30)"}, {"InvNo": "A02426850", "CustName": "吴先生", "ComeDate": "20250717", "ComeTime": "17:52:59", "CloseDatetime": "2019-07-07 16:12:56.520000", "Tot": 283, "RmNo": "806", "Numbers": 2, "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Remark": "贰位，V返", "实际消费时长": "-52849.7小时", "消费分钟数": -3170980, "跨越时间段": [["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"]], "跨越时间段数量": 3, "真实直落状态": "✅ 真正直落", "直落类型": "跨越3个时间段", "预约状态": "预约单时间段 (18:00-21:00)"}, {"InvNo": "A02426850", "CustName": "吴先生", "ComeDate": "20250717", "ComeTime": "17:52:59", "CloseDatetime": "2021-01-23 17:38:17.600000", "Tot": 3, "RmNo": "806", "Numbers": 2, "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Remark": "贰位，V返", "实际消费时长": "-39264.2小时", "消费分钟数": -2355854, "跨越时间段": [["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"]], "跨越时间段数量": 4, "真实直落状态": "✅ 真正直落", "直落类型": "跨越4个时间段", "预约状态": "预约单时间段 (18:00-21:00)"}, {"InvNo": "A02426850", "CustName": "吴先生", "ComeDate": "20250717", "ComeTime": "17:52:59", "CloseDatetime": "2021-11-28 15:34:58.313000", "Tot": 425, "RmNo": "806", "Numbers": 2, "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Remark": "贰位，V返", "实际消费时长": "-31850.3小时", "消费分钟数": -1911018, "跨越时间段": [["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"]], "跨越时间段数量": 3, "真实直落状态": "✅ 真正直落", "直落类型": "跨越3个时间段", "预约状态": "预约单时间段 (18:00-21:00)"}, {"InvNo": "A02426850", "CustName": "吴先生", "ComeDate": "20250717", "ComeTime": "17:52:59", "CloseDatetime": "2022-02-10 19:31:45.243000", "Tot": 1160, "RmNo": "806", "Numbers": 2, "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Remark": "贰位，V返", "实际消费时长": "-30070.4小时", "消费分钟数": -1804221, "跨越时间段": [["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 8, "真实直落状态": "✅ 真正直落", "直落类型": "跨越8个时间段", "预约状态": "预约单时间段 (18:00-21:00)"}, {"InvNo": "A02426850", "CustName": "吴先生", "ComeDate": "20250717", "ComeTime": "17:52:59", "CloseDatetime": "2023-07-09 20:38:57.993000", "Tot": 807, "RmNo": "806", "Numbers": 2, "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Remark": "贰位，V返", "实际消费时长": "-17733.2小时", "消费分钟数": -1063994, "跨越时间段": [["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 8, "真实直落状态": "✅ 真正直落", "直落类型": "跨越8个时间段", "预约状态": "预约单时间段 (18:00-21:00)"}, {"InvNo": "A02426850", "CustName": "吴先生", "ComeDate": "20250717", "ComeTime": "17:52:59", "CloseDatetime": "2025-07-17 20:18:57.193000", "Tot": 346, "RmNo": "806", "Numbers": 2, "Beg_Name": "18:00-21:00", "End_Name": "18:00-21:00", "Remark": "贰位，V返", "实际消费时长": "2.4小时", "消费分钟数": 145, "跨越时间段": [["05", "20:00"], ["07", "15:00-18:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 8, "真实直落状态": "✅ 真正直落", "直落类型": "跨越8个时间段", "预约状态": "预约单时间段 (18:00-21:00)"}, {"InvNo": "A02426864", "CustName": "梁女士", "ComeDate": "20250717", "ComeTime": "19:28:06", "CloseDatetime": "2019-07-07 19:20:26.243000", "Tot": 114, "RmNo": "801", "Numbers": 3, "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Remark": "肆位，其中一位半价", "实际消费时长": "-52848.1小时", "消费分钟数": -3170887, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 7, "真实直落状态": "✅ 真正直落", "直落类型": "跨越7个时间段", "预约状态": "预约单时间段 (19:00-22:00)"}, {"InvNo": "A02426864", "CustName": "梁女士", "ComeDate": "20250717", "ComeTime": "19:28:06", "CloseDatetime": "2021-01-23 19:19:44.200000", "Tot": 952, "RmNo": "801", "Numbers": 3, "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Remark": "肆位，其中一位半价", "实际消费时长": "-39264.1小时", "消费分钟数": -2355848, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 7, "真实直落状态": "✅ 真正直落", "直落类型": "跨越7个时间段", "预约状态": "预约单时间段 (19:00-22:00)"}, {"InvNo": "A02426864", "CustName": "梁女士", "ComeDate": "20250717", "ComeTime": "19:28:06", "CloseDatetime": "2021-11-28 15:39:06.753000", "Tot": 170, "RmNo": "801", "Numbers": 3, "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Remark": "肆位，其中一位半价", "实际消费时长": "-31851.8小时", "消费分钟数": -1911108, "跨越时间段": [["05", "20:00"], ["14", "01:00"]], "跨越时间段数量": 2, "真实直落状态": "✅ 真正直落", "直落类型": "跨越2个时间段", "预约状态": "预约单时间段 (19:00-22:00)"}, {"InvNo": "A02426864", "CustName": "梁女士", "ComeDate": "20250717", "ComeTime": "19:28:06", "CloseDatetime": "2022-02-10 18:31:23.410000", "Tot": 117, "RmNo": "801", "Numbers": 3, "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Remark": "肆位，其中一位半价", "实际消费时长": "-30072.9小时", "消费分钟数": -1804376, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"]], "跨越时间段数量": 5, "真实直落状态": "✅ 真正直落", "直落类型": "跨越5个时间段", "预约状态": "预约单时间段 (19:00-22:00)"}, {"InvNo": "A02426864", "CustName": "梁女士", "ComeDate": "20250717", "ComeTime": "19:28:06", "CloseDatetime": "2023-07-09 20:40:05.697000", "Tot": 302, "RmNo": "801", "Numbers": 3, "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Remark": "肆位，其中一位半价", "实际消费时长": "-17734.8小时", "消费分钟数": -1064088, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 7, "真实直落状态": "✅ 真正直落", "直落类型": "跨越7个时间段", "预约状态": "预约单时间段 (19:00-22:00)"}, {"InvNo": "A02426864", "CustName": "梁女士", "ComeDate": "20250717", "ComeTime": "19:28:06", "CloseDatetime": "2025-07-17 21:36:24.257000", "Tot": 568, "RmNo": "801", "Numbers": 3, "Beg_Name": "19:00-22:00", "End_Name": "19:00-22:00", "Remark": "肆位，其中一位半价", "实际消费时长": "2.1小时", "消费分钟数": 128, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["25", "17:00-20:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 7, "真实直落状态": "✅ 真正直落", "直落类型": "跨越7个时间段", "预约状态": "预约单时间段 (19:00-22:00)"}, {"InvNo": "A02426869", "CustName": "贵宾小姐女士女士", "ComeDate": "20250717", "ComeTime": "20:56:23", "CloseDatetime": "2019-07-07 19:38:08.607000", "Tot": 675, "RmNo": "555", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "6位，中房畅饮", "实际消费时长": "-52849.3小时", "消费分钟数": -3170958, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 6, "真实直落状态": "✅ 真正直落", "直落类型": "跨越6个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426869", "CustName": "贵宾小姐女士女士", "ComeDate": "20250717", "ComeTime": "20:56:23", "CloseDatetime": "2021-01-24 01:12:14.760000", "Tot": 1916, "RmNo": "555", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "6位，中房畅饮", "实际消费时长": "-39259.7小时", "消费分钟数": -2355584, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 6, "真实直落状态": "✅ 真正直落", "直落类型": "跨越6个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426869", "CustName": "贵宾小姐女士女士", "ComeDate": "20250717", "ComeTime": "20:56:23", "CloseDatetime": "2021-11-28 16:00:59.243000", "Tot": 0, "RmNo": "555", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "6位，中房畅饮", "实际消费时长": "-31852.9小时", "消费分钟数": -1911175, "跨越时间段": [["05", "20:00"], ["14", "01:00"]], "跨越时间段数量": 2, "真实直落状态": "✅ 真正直落", "直落类型": "跨越2个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426869", "CustName": "贵宾小姐女士女士", "ComeDate": "20250717", "ComeTime": "20:56:23", "CloseDatetime": "2022-02-10 19:18:02.743000", "Tot": 496, "RmNo": "555", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "6位，中房畅饮", "实际消费时长": "-30073.6小时", "消费分钟数": -1804418, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 6, "真实直落状态": "✅ 真正直落", "直落类型": "跨越6个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426869", "CustName": "贵宾小姐女士女士", "ComeDate": "20250717", "ComeTime": "20:56:23", "CloseDatetime": "2023-07-09 20:34:01.817000", "Tot": 222, "RmNo": "555", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "6位，中房畅饮", "实际消费时长": "-17736.4小时", "消费分钟数": -1064182, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 6, "真实直落状态": "✅ 真正直落", "直落类型": "跨越6个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426869", "CustName": "贵宾小姐女士女士", "ComeDate": "20250717", "ComeTime": "20:56:23", "CloseDatetime": "2025-07-18 02:05:39.290000", "Tot": 3543, "RmNo": "555", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "6位，中房畅饮", "实际消费时长": "5.2小时", "消费分钟数": 309, "跨越时间段": [["05", "20:00"], ["14", "01:00"], ["29", "18:10-21:10"], ["37", "18:00-21:00"], ["39", "19:00-22:00"], ["46", "19:00-21:30"]], "跨越时间段数量": 6, "真实直落状态": "✅ 真正直落", "直落类型": "跨越6个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426880", "CustName": "林先生", "ComeDate": "20250717", "ComeTime": "22:00:56", "CloseDatetime": "2019-07-08 02:03:43.990000", "Tot": 566, "RmNo": "831", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "5位，现买", "实际消费时长": "-52844.0小时", "消费分钟数": -3170637, "跨越时间段": [["05", "20:00"], ["14", "01:00"]], "跨越时间段数量": 2, "真实直落状态": "✅ 真正直落", "直落类型": "跨越2个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426880", "CustName": "林先生", "ComeDate": "20250717", "ComeTime": "22:00:56", "CloseDatetime": "2021-01-23 21:07:41.907000", "Tot": 180, "RmNo": "831", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "5位，现买", "实际消费时长": "-39264.9小时", "消费分钟数": -2355893, "跨越时间段": [["05", "20:00"], ["14", "01:00"]], "跨越时间段数量": 2, "真实直落状态": "✅ 真正直落", "直落类型": "跨越2个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426880", "CustName": "林先生", "ComeDate": "20250717", "ComeTime": "22:00:56", "CloseDatetime": "2021-11-28 19:10:52.760000", "Tot": 664, "RmNo": "831", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "5位，现买", "实际消费时长": "-31850.8小时", "消费分钟数": -1911050, "跨越时间段": [["05", "20:00"], ["14", "01:00"]], "跨越时间段数量": 2, "真实直落状态": "✅ 真正直落", "直落类型": "跨越2个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426880", "CustName": "林先生", "ComeDate": "20250717", "ComeTime": "22:00:56", "CloseDatetime": "2022-02-10 19:31:19.897000", "Tot": 435, "RmNo": "831", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "5位，现买", "实际消费时长": "-30074.5小时", "消费分钟数": -1804469, "跨越时间段": [["05", "20:00"], ["14", "01:00"]], "跨越时间段数量": 2, "真实直落状态": "✅ 真正直落", "直落类型": "跨越2个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426880", "CustName": "林先生", "ComeDate": "20250717", "ComeTime": "22:00:56", "CloseDatetime": "2023-07-09 20:31:02.133000", "Tot": 0, "RmNo": "831", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "5位，现买", "实际消费时长": "-17737.5小时", "消费分钟数": -1064249, "跨越时间段": [["05", "20:00"], ["14", "01:00"]], "跨越时间段数量": 2, "真实直落状态": "✅ 真正直落", "直落类型": "跨越2个时间段", "预约状态": "预约单时间段 (20:00)"}, {"InvNo": "A02426880", "CustName": "林先生", "ComeDate": "20250717", "ComeTime": "22:00:56", "CloseDatetime": "2025-07-18 01:55:13.710000", "Tot": 1592, "RmNo": "831", "Numbers": 2, "Beg_Name": "20:00", "End_Name": "20:00", "Remark": "5位，现买", "实际消费时长": "3.9小时", "消费分钟数": 234, "跨越时间段": [["05", "20:00"], ["14", "01:00"]], "跨越时间段数量": 2, "真实直落状态": "✅ 真正直落", "直落类型": "跨越2个时间段", "预约状态": "预约单时间段 (20:00)"}]}