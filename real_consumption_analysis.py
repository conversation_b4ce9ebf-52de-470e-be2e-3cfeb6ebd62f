#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于实际消费时长的KTV分析
通过ComeTime到CloseDatetime的时间跨度判断真正的直落情况
"""

import pyodbc
import pandas as pd
from datetime import datetime, timedelta
import json

class RealConsumptionAnalyzer:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
        # 名堂店时间段配置
        self.time_slots = {
            '02': {'name': '13:30-16:30', 'start': '13:30', 'end': '16:30'},
            '05': {'name': '20:00', 'start': '20:00', 'end': '06:00'},  # 跨天
            '07': {'name': '15:00-18:00', 'start': '15:00', 'end': '18:00'},
            '14': {'name': '01:00', 'start': '01:00', 'end': '06:00'},
            '25': {'name': '17:00-20:00', 'start': '17:00', 'end': '20:00'},
            '28': {'name': '11:50-14:50', 'start': '11:50', 'end': '14:50'},
            '29': {'name': '18:10-21:10', 'start': '18:10', 'end': '21:10'},
            '37': {'name': '18:00-21:00', 'start': '18:00', 'end': '21:00'},
            '39': {'name': '19:00-22:00', 'start': '19:00', 'end': '22:00'},
            '46': {'name': '19:00-21:30', 'start': '19:00', 'end': '21:30'}
        }
        
        # 用户提供的样本订单
        self.sample_orders = [
            '*********', '*********', '*********', '*********', '*********',
            '*********', '*********', '*********', '*********', '*********', '*********'
        ]
        
    def get_connection(self, server, database, username, password):
        """获取数据库连接"""
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER={server};
        DATABASE={database};
        UID={username};
        PWD={password};
        """
        return pyodbc.connect(conn_str)
    
    def time_to_minutes(self, time_str):
        """将时间字符串转换为分钟数（从00:00开始计算）"""
        if not time_str or time_str == '':
            return None
        
        try:
            hour, minute = map(int, time_str.split(':'))
            # 处理跨天情况，如果小时数小于6，认为是第二天
            if hour < 6:
                hour += 24
            return hour * 60 + minute
        except:
            return None
    
    def get_overlapping_time_slots(self, start_time, end_time):
        """获取消费时间跨越的所有时间段"""
        overlapping_slots = []
        
        start_minutes = self.time_to_minutes(start_time)
        end_minutes = self.time_to_minutes(end_time)
        
        if start_minutes is None or end_minutes is None:
            return overlapping_slots
        
        for slot_no, slot_info in self.time_slots.items():
            slot_start = self.time_to_minutes(slot_info['start'])
            slot_end = self.time_to_minutes(slot_info['end'])
            
            if slot_start is None or slot_end is None:
                continue
            
            # 检查是否有重叠
            if slot_end < slot_start:  # 跨天时间段
                # 跨天时间段的重叠判断
                if (start_minutes <= slot_end or start_minutes >= slot_start) or \
                   (end_minutes <= slot_end or end_minutes >= slot_start) or \
                   (start_minutes <= slot_start and end_minutes >= slot_end):
                    overlapping_slots.append((slot_no, slot_info['name']))
            else:  # 同一天时间段
                # 标准重叠判断
                if not (end_minutes <= slot_start or start_minutes >= slot_end):
                    overlapping_slots.append((slot_no, slot_info['name']))
        
        return overlapping_slots
    
    def get_sample_data_with_checkout(self):
        """获取样本订单的开台和结账数据"""
        try:
            # 获取开台数据
            conn = self.get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
            
            order_list = "', '".join(self.sample_orders)
            open_query = f"""
            SELECT 
                Ikey, BookNo, ShopId, CustName, CustTel, ComeDate, ComeTime,
                Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName,
                RmNo, Invno, Remark
            FROM opencacheinfo
            WHERE Invno IN ('{order_list}')
            ORDER BY ComeTime
            """
            
            open_df = pd.read_sql(open_query, conn)
            conn.close()
            
            # 获取结账数据
            conn = self.get_connection('192.168.2.5', 'operatedata', 'sa', 'Musicbox123')
            
            close_query = f"""
            SELECT Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
            FROM rmcloseinfo 
            WHERE InvNo IN ('{order_list}')
            ORDER BY CloseDatetime
            """
            
            close_df = pd.read_sql(close_query, conn)
            conn.close()
            
            # 关联数据
            merged_df = pd.merge(open_df, close_df, left_on='Invno', right_on='InvNo', how='left')
            
            return merged_df
            
        except Exception as e:
            print(f"获取数据失败: {e}")
            return pd.DataFrame()
    
    def analyze_real_consumption(self):
        """基于实际消费时长分析直落情况"""
        print(f"\n{'='*80}")
        print(f"基于实际消费时长的直落分析")
        print(f"样本订单: {len(self.sample_orders)} 个")
        print(f"{'='*80}")
        
        # 获取数据
        df = self.get_sample_data_with_checkout()
        
        if df.empty:
            print("没有获取到数据")
            return {}
        
        print(f"\n📊 获取到 {len(df)} 个订单的完整数据")
        
        analysis_results = []
        
        for _, row in df.iterrows():
            result = {
                'InvNo': row['InvNo'],
                'CustName': row['CustName'],
                'ComeDate': row['ComeDate'],
                'ComeTime': row['ComeTime'],
                'CloseDatetime': row['CloseDatetime'],
                'Tot': row['Tot'],
                'RmNo': row['RmNo'],
                'Numbers': row['Numbers'],
                'Beg_Name': row['Beg_Name'],
                'End_Name': row['End_Name'],
                'Remark': row['Remark']
            }
            
            # 计算实际消费时长
            if pd.notna(row['CloseDatetime']):
                try:
                    # 构建完整的开台时间
                    come_datetime = datetime.strptime(f"{row['ComeDate']} {row['ComeTime']}", '%Y%m%d %H:%M:%S')
                    close_datetime = pd.to_datetime(row['CloseDatetime'])
                    
                    # 计算消费时长
                    duration = close_datetime - come_datetime
                    duration_hours = duration.total_seconds() / 3600
                    
                    result['实际消费时长'] = f"{duration_hours:.1f}小时"
                    result['消费分钟数'] = int(duration.total_seconds() / 60)
                    
                    # 获取消费时间跨越的时间段
                    start_time = row['ComeTime'][:5]  # HH:MM
                    end_time = close_datetime.strftime('%H:%M')
                    
                    overlapping_slots = self.get_overlapping_time_slots(start_time, end_time)
                    result['跨越时间段'] = overlapping_slots
                    result['跨越时间段数量'] = len(overlapping_slots)
                    
                    # 判断是否真正直落
                    if len(overlapping_slots) > 1:
                        result['真实直落状态'] = '✅ 真正直落'
                        result['直落类型'] = f"跨越{len(overlapping_slots)}个时间段"
                    else:
                        result['真实直落状态'] = '单时间段消费'
                        result['直落类型'] = '非直落'
                    
                    # 预约vs实际对比
                    if row['Beg_Name'] != row['End_Name']:
                        result['预约状态'] = f"预约直落 ({row['Beg_Name']} → {row['End_Name']})"
                    else:
                        result['预约状态'] = f"预约单时间段 ({row['Beg_Name']})"
                    
                except Exception as e:
                    result['实际消费时长'] = f"计算错误: {e}"
                    result['真实直落状态'] = '无法判断'
                    result['跨越时间段'] = []
                    result['跨越时间段数量'] = 0
            else:
                result['实际消费时长'] = '未结账'
                result['真实直落状态'] = '未结账'
                result['跨越时间段'] = []
                result['跨越时间段数量'] = 0
            
            analysis_results.append(result)
        
        # 统计分析
        total_orders = len(analysis_results)
        real_direct_fall = len([r for r in analysis_results if r['真实直落状态'] == '✅ 真正直落'])
        single_slot = len([r for r in analysis_results if r['真实直落状态'] == '单时间段消费'])
        
        print(f"\n📈 分析结果统计:")
        print(f"  总订单数: {total_orders}")
        print(f"  真正直落订单: {real_direct_fall}")
        print(f"  单时间段消费: {single_slot}")
        print(f"  真实直落率: {real_direct_fall/total_orders*100:.1f}%" if total_orders > 0 else "  真实直落率: N/A")
        
        # 详细分析
        print(f"\n🔍 详细分析:")
        print(f"{'='*140}")
        print(f"{'订单号':<12} {'客户':<10} {'开台时间':<10} {'结账时间':<10} {'消费时长':<10} {'跨越时间段':<8} {'状态':<12} {'金额':<8}")
        print("-" * 140)
        
        for result in analysis_results:
            close_time = result['CloseDatetime'].strftime('%H:%M') if pd.notna(result['CloseDatetime']) else '未结账'
            
            print(f"{result['InvNo']:<12} "
                  f"{result['CustName']:<10} "
                  f"{result['ComeTime'][:5]:<10} "
                  f"{close_time:<10} "
                  f"{result['实际消费时长']:<10} "
                  f"{result['跨越时间段数量']:<8} "
                  f"{result['真实直落状态']:<12} "
                  f"¥{result['Tot']:<7.0f}" if pd.notna(result['Tot']) else f"{'未结账':<8}")
        
        # 直落订单详情
        direct_fall_orders = [r for r in analysis_results if r['真实直落状态'] == '✅ 真正直落']
        if direct_fall_orders:
            print(f"\n✅ 真正直落订单详情:")
            print(f"{'='*80}")
            for order in direct_fall_orders:
                print(f"\n【{order['InvNo']} - {order['CustName']}】")
                print(f"  开台时间: {order['ComeTime']}")
                print(f"  结账时间: {order['CloseDatetime']}")
                print(f"  消费时长: {order['实际消费时长']}")
                print(f"  预约情况: {order['预约状态']}")
                print(f"  跨越时间段: {[slot[1] for slot in order['跨越时间段']]}")
                print(f"  金额: ¥{order['Tot']:.0f}" if pd.notna(order['Tot']) else "  金额: 未结账")
                if pd.notna(order['Remark']):
                    print(f"  备注: {order['Remark']}")
        
        return {
            'total_orders': total_orders,
            'real_direct_fall': real_direct_fall,
            'single_slot': single_slot,
            'analysis_results': analysis_results
        }

def main():
    analyzer = RealConsumptionAnalyzer()
    
    try:
        # 基于实际消费时长的分析
        result = analyzer.analyze_real_consumption()
        
        # 保存结果
        if result:
            with open('real_consumption_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n✅ 分析完成！")
            print(f"📄 详细结果已保存到: real_consumption_analysis.json")
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
