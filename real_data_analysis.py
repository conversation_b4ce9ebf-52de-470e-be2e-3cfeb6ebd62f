#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实开台数据的KTV分析脚本
使用Beg_Name和End_Name字段准确识别直落订单
"""

import pyodbc
import pandas as pd
from datetime import datetime
import json

class RealDataAnalyzer:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
    def get_connection(self, server, database, username, password):
        """获取数据库连接"""
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER={server};
        DATABASE={database};
        UID={username};
        PWD={password};
        """
        return pyodbc.connect(conn_str)
    
    def get_real_data(self, work_date):
        """获取真实的开台和结账关联数据"""
        try:
            # 获取开台数据
            conn = self.get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
            
            open_query = f"""
            SELECT 
                Ikey, BookNo, ShopId, <PERSON>ust<PERSON>ame, <PERSON>ust<PERSON>el, ComeDate, ComeTime,
                Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName,
                RmNo, Invno, Remark
            FROM opencacheinfo
            WHERE shopid = {self.shop_id} AND ComeDate = '{work_date}'
            ORDER BY ComeTime
            """
            
            open_df = pd.read_sql(open_query, conn)
            conn.close()
            
            # 获取结账数据
            conn = self.get_connection('192.168.2.5', 'operatedata', 'sa', 'Musicbox123')
            
            close_query = f"""
            SELECT Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
            FROM rmcloseinfo 
            WHERE shopid = {self.shop_id} AND WorkDate = '{work_date}'
            ORDER BY CloseDatetime
            """
            
            close_df = pd.read_sql(close_query, conn)
            conn.close()
            
            # 关联开台和结账数据
            merged_df = pd.merge(open_df, close_df, left_on='Invno', right_on='InvNo', how='left')
            
            return merged_df
            
        except Exception as e:
            print(f"获取数据失败: {e}")
            return pd.DataFrame()
    
    def identify_channel(self, vesa_name, remark):
        """识别消费渠道"""
        # 先检查备注
        if pd.notna(remark):
            remark_str = str(remark).lower()
            if '美团' in remark_str:
                return '美团'
            elif '抖音' in remark_str:
                return '抖音'
            elif '特权' in remark_str:
                return '特权预约'
        
        # 再检查支付方式
        if pd.notna(vesa_name):
            vesa_str = str(vesa_name).lower()
            if '美团' in vesa_str:
                return '美团'
            elif '抖音' in vesa_str:
                return '抖音'
            elif '特权' in vesa_str:
                return '特权预约'
        
        return 'K+'
    
    def analyze_real_direct_fall(self, work_date):
        """基于真实数据分析直落订单"""
        print(f"\n{'='*60}")
        print(f"基于真实数据的KTV分析 - {work_date}")
        print(f"门店: 名堂店 (ID: {self.shop_id})")
        print(f"{'='*60}")
        
        # 获取数据
        df = self.get_real_data(work_date)
        
        if df.empty:
            print("没有数据")
            return {}
        
        print(f"\n数据概览:")
        print(f"  总记录数: {len(df)}")
        print(f"  有结账数据: {len(df[df['Tot'].notna()])}")
        print(f"  总营业额: ¥{df['Tot'].sum():,.0f}")
        
        # 添加渠道信息
        df['Channel'] = df.apply(lambda row: self.identify_channel(row['VesaName'], row['Remark']), axis=1)
        
        # 识别直落订单
        direct_fall_df = df[df['Beg_Name'] != df['End_Name']].copy()
        single_slot_df = df[df['Beg_Name'] == df['End_Name']].copy()
        
        print(f"\n直落订单识别:")
        print(f"  真正直落订单: {len(direct_fall_df)} 个")
        print(f"  单时间段订单: {len(single_slot_df)} 个")
        
        # 分析直落订单
        if not direct_fall_df.empty:
            print(f"\n{'='*60}")
            print("真正的直落订单详情")
            print(f"{'='*60}")
            
            for _, row in direct_fall_df.iterrows():
                print(f"\n【{row['InvNo']} - {row['CustName']}】")
                print(f"  开台时间: {row['ComeTime']}")
                print(f"  起始时间段: {row['Beg_Name']}")
                print(f"  结束时间段: {row['End_Name']}")
                print(f"  房间号: {row['RmNo']}")
                print(f"  人数: {row['Numbers']}")
                print(f"  结账时间: {row['CloseDatetime']}")
                print(f"  金额: ¥{row['Tot']:.0f}" if pd.notna(row['Tot']) else "  金额: 未结账")
                print(f"  渠道: {row['Channel']}")
                if pd.notna(row['Remark']):
                    print(f"  备注: {row['Remark']}")
        
        # 按时间段统计
        time_slot_analysis = {}
        
        # 统计各时间段的订单情况
        all_slots = set(df['Beg_Name'].dropna()) | set(df['End_Name'].dropna())
        
        for slot in all_slots:
            if pd.isna(slot):
                continue
                
            # 该时间段开始的订单
            start_orders = df[df['Beg_Name'] == slot]
            # 该时间段结束的订单（直落到此时间段）
            end_orders = df[(df['End_Name'] == slot) & (df['Beg_Name'] != slot)]
            
            if not start_orders.empty or not end_orders.empty:
                time_slot_analysis[slot] = {
                    'start_orders': len(start_orders),
                    'direct_fall_in': len(end_orders),
                    'total_revenue': start_orders['Tot'].sum() + end_orders['Tot'].sum(),
                    'channel_distribution': {}
                }
                
                # 渠道分布
                all_orders = pd.concat([start_orders, end_orders])
                channel_counts = all_orders['Channel'].value_counts()
                for channel, count in channel_counts.items():
                    channel_revenue = all_orders[all_orders['Channel'] == channel]['Tot'].sum()
                    time_slot_analysis[slot]['channel_distribution'][channel] = {
                        'count': count,
                        'revenue': channel_revenue
                    }
        
        # 生成报表
        print(f"\n{'='*80}")
        print("时间段分析报表")
        print(f"{'='*80}")
        print(f"{'时间段':<15} {'开始订单':<8} {'直落进入':<8} {'营业额':<12} {'主要渠道':<15}")
        print("-" * 80)
        
        for slot, data in time_slot_analysis.items():
            main_channel = max(data['channel_distribution'].items(), 
                             key=lambda x: x[1]['count'])[0] if data['channel_distribution'] else 'N/A'
            
            print(f"{slot:<15} "
                  f"{data['start_orders']:<8} "
                  f"{data['direct_fall_in']:<8} "
                  f"¥{data['total_revenue']:<11,.0f} "
                  f"{main_channel:<15}")
        
        # 分析结果
        analysis_result = {
            'work_date': work_date,
            'shop_id': self.shop_id,
            'summary': {
                'total_records': len(df),
                'total_revenue': int(df['Tot'].sum()),
                'direct_fall_orders': len(direct_fall_df),
                'single_slot_orders': len(single_slot_df),
                'channel_distribution': df['Channel'].value_counts().to_dict()
            },
            'direct_fall_details': direct_fall_df[['InvNo', 'CustName', 'ComeTime', 'Beg_Name', 'End_Name', 'Tot', 'Channel', 'Remark']].to_dict('records'),
            'time_slot_analysis': time_slot_analysis
        }
        
        return analysis_result
    
    def validate_my_algorithm(self, work_date):
        """验证我之前算法的准确性"""
        print(f"\n{'='*60}")
        print("验证我的估算算法准确性")
        print(f"{'='*60}")
        
        df = self.get_real_data(work_date)
        
        if df.empty:
            return
        
        # 我之前识别为直落的订单
        my_identified = [
            'A02426811', 'A02426810', 'A02426807', 'A02426809', 'A02426808', 
            'A02426814', 'A02426813', 'A02426812', 'A02426806'
        ]
        
        # 真正的直落订单
        real_direct_fall = df[df['Beg_Name'] != df['End_Name']]['Invno'].tolist()
        
        print(f"我识别的直落订单: {len(my_identified)} 个")
        print(f"真正的直落订单: {len(real_direct_fall)} 个")
        
        # 准确率分析
        correct_predictions = set(my_identified) & set(real_direct_fall)
        false_positives = set(my_identified) - set(real_direct_fall)
        false_negatives = set(real_direct_fall) - set(my_identified)
        
        print(f"\n准确性分析:")
        print(f"  正确识别: {len(correct_predictions)} 个")
        print(f"  误判为直落: {len(false_positives)} 个")
        print(f"  漏识别直落: {len(false_negatives)} 个")
        
        if len(my_identified) > 0:
            precision = len(correct_predictions) / len(my_identified)
            print(f"  精确率: {precision:.2%}")
        
        if len(real_direct_fall) > 0:
            recall = len(correct_predictions) / len(real_direct_fall)
            print(f"  召回率: {recall:.2%}")
        
        print(f"\n详细对比:")
        for invno in my_identified:
            row = df[df['Invno'] == invno]
            if not row.empty:
                row = row.iloc[0]
                is_real_direct_fall = row['Beg_Name'] != row['End_Name']
                status = "✅ 正确" if is_real_direct_fall else "❌ 误判"
                print(f"  {invno}: {status} - {row['Beg_Name']} → {row['End_Name']}")

def main():
    analyzer = RealDataAnalyzer()
    
    work_date = '20250717'
    
    try:
        # 基于真实数据的分析
        result = analyzer.analyze_real_direct_fall(work_date)
        
        # 验证我的算法
        analyzer.validate_my_algorithm(work_date)
        
        # 保存结果
        if result:
            with open(f'real_data_analysis_{work_date}.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n分析结果已保存到: real_data_analysis_{work_date}.json")
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
