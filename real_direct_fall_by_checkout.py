#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实结账时间的KTV直落分析
考虑客人可能中途取消的情况，通过结账时间来判断真正的直落消费
"""

import pyodbc
import pandas as pd
from datetime import datetime, time
import json

class RealDirectFallByCheckout:
    def __init__(self):
        self.shop_id = 11  # 名堂店
        
        # 名堂店时间段配置 (从数据库查询得到)
        self.time_slots = {
            '02': {'name': '13:30-16:30', 'start': '13:30', 'end': '16:30'},
            '05': {'name': '20:00', 'start': '20:00', 'end': '06:00'},  # 跨天
            '07': {'name': '15:00-18:00', 'start': '15:00', 'end': '18:00'},
            '14': {'name': '01:00', 'start': '01:00', 'end': '06:00'},
            '25': {'name': '17:00-20:00', 'start': '17:00', 'end': '20:00'},
            '28': {'name': '11:50-14:50', 'start': '11:50', 'end': '14:50'},
            '29': {'name': '18:10-21:10', 'start': '18:10', 'end': '21:10'},
            '37': {'name': '18:00-21:00', 'start': '18:00', 'end': '21:00'},
            '39': {'name': '19:00-22:00', 'start': '19:00', 'end': '22:00'},
            '46': {'name': '19:00-21:30', 'start': '19:00', 'end': '21:30'}
        }
        
    def get_connection(self, server, database, username, password):
        """获取数据库连接"""
        conn_str = f"""
        DRIVER={{ODBC Driver 17 for SQL Server}};
        SERVER={server};
        DATABASE={database};
        UID={username};
        PWD={password};
        """
        return pyodbc.connect(conn_str)
    
    def time_to_minutes(self, time_str):
        """将时间字符串转换为分钟数（从00:00开始计算）"""
        if not time_str or time_str == '':
            return None
        
        try:
            hour, minute = map(int, time_str.split(':'))
            # 处理跨天情况，如果小时数小于6，认为是第二天
            if hour < 6:
                hour += 24
            return hour * 60 + minute
        except:
            return None
    
    def is_time_in_slot(self, check_time, slot_start, slot_end):
        """判断时间是否在时间段内"""
        check_minutes = self.time_to_minutes(check_time)
        start_minutes = self.time_to_minutes(slot_start)
        end_minutes = self.time_to_minutes(slot_end)
        
        if None in [check_minutes, start_minutes, end_minutes]:
            return False
        
        # 处理跨天情况
        if end_minutes < start_minutes:
            # 跨天时间段
            return check_minutes >= start_minutes or check_minutes <= end_minutes
        else:
            # 同一天时间段
            return start_minutes <= check_minutes <= end_minutes
    
    def get_time_slot_by_checkout(self, checkout_time):
        """根据结账时间判断属于哪个时间段"""
        if not checkout_time:
            return None
        
        # 提取时间部分
        if isinstance(checkout_time, str):
            try:
                checkout_time = datetime.strptime(checkout_time, '%Y-%m-%d %H:%M:%S')
            except:
                return None
        
        check_time_str = checkout_time.strftime('%H:%M')
        
        # 检查每个时间段
        for slot_no, slot_info in self.time_slots.items():
            if self.is_time_in_slot(check_time_str, slot_info['start'], slot_info['end']):
                return slot_no, slot_info['name']
        
        return None, None
    
    def get_complete_data(self, work_date):
        """获取完整的开台和结账数据"""
        try:
            # 获取开台数据
            conn = self.get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
            
            open_query = f"""
            SELECT 
                Ikey, BookNo, ShopId, CustName, CustTel, ComeDate, ComeTime,
                Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName,
                RmNo, Invno, Remark
            FROM opencacheinfo
            WHERE shopid = {self.shop_id} AND ComeDate = '{work_date}'
            ORDER BY ComeTime
            """
            
            open_df = pd.read_sql(open_query, conn)
            conn.close()
            
            # 获取结账数据
            conn = self.get_connection('192.168.2.5', 'operatedata', 'sa', 'Musicbox123')
            
            close_query = f"""
            SELECT Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
            FROM rmcloseinfo 
            WHERE shopid = {self.shop_id} AND WorkDate = '{work_date}'
            ORDER BY CloseDatetime
            """
            
            close_df = pd.read_sql(close_query, conn)
            conn.close()
            
            # 关联数据
            merged_df = pd.merge(open_df, close_df, left_on='Invno', right_on='InvNo', how='left')
            
            return merged_df
            
        except Exception as e:
            print(f"获取数据失败: {e}")
            return pd.DataFrame()
    
    def analyze_real_direct_fall(self, work_date):
        """基于真实结账时间分析直落订单"""
        print(f"\n{'='*80}")
        print(f"基于真实结账时间的直落分析 - {work_date}")
        print(f"门店: 名堂店 (ID: {self.shop_id})")
        print(f"{'='*80}")
        
        # 获取数据
        df = self.get_complete_data(work_date)
        
        if df.empty:
            print("没有数据")
            return {}
        
        print(f"\n📊 名堂店时间段配置:")
        print(f"{'='*60}")
        for slot_no, slot_info in sorted(self.time_slots.items()):
            print(f"  {slot_no}: {slot_info['name']} ({slot_info['start']}-{slot_info['end']})")
        
        # 分析每个订单
        analysis_results = []
        
        for _, row in df.iterrows():
            result = {
                'InvNo': row['InvNo'],
                'CustName': row['CustName'],
                'ComeTime': row['ComeTime'],
                'Beg_Name': row['Beg_Name'],
                'End_Name': row['End_Name'],
                'CloseDatetime': row['CloseDatetime'],
                'Tot': row['Tot'],
                'RmNo': row['RmNo'],
                'Remark': row['Remark']
            }
            
            # 预约情况
            if pd.notna(row['Beg_Name']) and pd.notna(row['End_Name']):
                if row['Beg_Name'] == row['End_Name']:
                    result['预约类型'] = '单时间段'
                else:
                    result['预约类型'] = f"预约直落 ({row['Beg_Name']} → {row['End_Name']})"
            else:
                result['预约类型'] = '未知'
            
            # 实际结账时间段
            if pd.notna(row['CloseDatetime']):
                checkout_slot_no, checkout_slot_name = self.get_time_slot_by_checkout(row['CloseDatetime'])
                result['实际结账时间段'] = checkout_slot_name if checkout_slot_name else '时间段外'
                result['结账时间段编号'] = checkout_slot_no
            else:
                result['实际结账时间段'] = '未结账'
                result['结账时间段编号'] = None
            
            # 判断是否真正直落
            if result['预约类型'].startswith('预约直落'):
                # 检查实际结账时间是否在预约的结束时间段内
                if pd.notna(row['End_Name']) and result['实际结账时间段']:
                    if row['End_Name'] == result['实际结账时间段']:
                        result['真实直落状态'] = '✅ 真正直落'
                    else:
                        result['真实直落状态'] = '❌ 中途取消'
                else:
                    result['真实直落状态'] = '❓ 无法判断'
            else:
                result['真实直落状态'] = '非直落预约'
            
            analysis_results.append(result)
        
        # 统计分析
        total_orders = len(analysis_results)
        booked_direct_fall = len([r for r in analysis_results if r['预约类型'].startswith('预约直落')])
        real_direct_fall = len([r for r in analysis_results if r['真实直落状态'] == '✅ 真正直落'])
        cancelled_direct_fall = len([r for r in analysis_results if r['真实直落状态'] == '❌ 中途取消'])
        
        print(f"\n📈 分析结果统计:")
        print(f"  总订单数: {total_orders}")
        print(f"  预约直落订单: {booked_direct_fall}")
        print(f"  真正直落订单: {real_direct_fall}")
        print(f"  中途取消订单: {cancelled_direct_fall}")
        print(f"  直落完成率: {real_direct_fall/booked_direct_fall*100:.1f}%" if booked_direct_fall > 0 else "  直落完成率: N/A")
        
        # 详细分析
        print(f"\n🔍 详细分析:")
        print(f"{'='*120}")
        print(f"{'订单号':<12} {'客户':<8} {'预约类型':<25} {'实际结账时间段':<15} {'状态':<12} {'金额':<8}")
        print("-" * 120)
        
        for result in analysis_results:
            if result['预约类型'].startswith('预约直落') or result['真实直落状态'] != '非直落预约':
                print(f"{result['InvNo']:<12} "
                      f"{result['CustName']:<8} "
                      f"{result['预约类型']:<25} "
                      f"{result['实际结账时间段']:<15} "
                      f"{result['真实直落状态']:<12} "
                      f"¥{result['Tot']:<7.0f}" if pd.notna(result['Tot']) else f"{'未结账':<8}")
        
        # 中途取消的详细分析
        cancelled_orders = [r for r in analysis_results if r['真实直落状态'] == '❌ 中途取消']
        if cancelled_orders:
            print(f"\n❌ 中途取消订单详情:")
            print(f"{'='*80}")
            for order in cancelled_orders:
                print(f"【{order['InvNo']} - {order['CustName']}】")
                print(f"  预约: {order['预约类型']}")
                print(f"  实际结账时间段: {order['实际结账时间段']}")
                print(f"  结账时间: {order['CloseDatetime']}")
                print(f"  金额: ¥{order['Tot']:.0f}" if pd.notna(order['Tot']) else "  金额: 未结账")
                if pd.notna(order['Remark']):
                    print(f"  备注: {order['Remark']}")
                print()
        
        # 真正直落订单详情
        real_direct_orders = [r for r in analysis_results if r['真实直落状态'] == '✅ 真正直落']
        if real_direct_orders:
            print(f"\n✅ 真正直落订单详情:")
            print(f"{'='*80}")
            for order in real_direct_orders:
                print(f"【{order['InvNo']} - {order['CustName']}】")
                print(f"  预约: {order['预约类型']}")
                print(f"  实际结账时间段: {order['实际结账时间段']}")
                print(f"  结账时间: {order['CloseDatetime']}")
                print(f"  金额: ¥{order['Tot']:.0f}" if pd.notna(order['Tot']) else "  金额: 未结账")
                if pd.notna(order['Remark']):
                    print(f"  备注: {order['Remark']}")
                print()
        
        return {
            'work_date': work_date,
            'shop_id': self.shop_id,
            'time_slots': self.time_slots,
            'summary': {
                'total_orders': total_orders,
                'booked_direct_fall': booked_direct_fall,
                'real_direct_fall': real_direct_fall,
                'cancelled_direct_fall': cancelled_direct_fall,
                'completion_rate': real_direct_fall/booked_direct_fall if booked_direct_fall > 0 else 0
            },
            'detailed_analysis': analysis_results
        }

def main():
    analyzer = RealDirectFallByCheckout()
    
    work_date = '20250717'
    
    try:
        # 基于真实结账时间的分析
        result = analyzer.analyze_real_direct_fall(work_date)
        
        # 保存结果
        if result:
            with open(f'real_direct_fall_checkout_{work_date}.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n✅ 分析完成！")
            print(f"📄 详细结果已保存到: real_direct_fall_checkout_{work_date}.json")
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
