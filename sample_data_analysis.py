#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析用户提供的开台数据样本
"""

# 从用户提供的INSERT语句中提取的数据
sample_data = [
    {
        'InvNo': 'A02426864',
        'CustName': '梁女士',
        'ComeDate': '20250717',
        'ComeTime': '19:28:06',
        'Beg_Key': '39',
        'Beg_Name': '19:00-22:00',
        'End_Key': '39',
        'End_Name': '19:00-22:00',
        'Numbers': 3,
        'RmNo': '801',
        'Remark': '肆位，其中一位半价'
    },
    {
        'InvNo': 'A02426880',
        'CustName': '林先生',
        'ComeDate': '20250717',
        'ComeTime': '22:00:56',
        'Beg_Key': '05',
        'Beg_Name': '20:00',
        'End_Key': '05',
        'End_Name': '20:00',
        'Numbers': 2,
        'RmNo': '831',
        'Remark': '5位，现买'
    },
    {
        'InvNo': 'A02426898',
        'CustName': '李女士',
        'ComeDate': '20250718',
        'ComeTime': '11:48:16',
        'Beg_Key': '28',
        'Beg_Name': '11:50-14:50',
        'End_Key': '28',
        'End_Name': '11:50-14:50',
        'Numbers': 5,
        'RmNo': '309',
        'Remark': '陆位（其中一位儿童）\nV返'
    },
    {
        'InvNo': 'A02426869',
        'CustName': '贵宾小姐女士女士',
        'ComeDate': '20250717',
        'ComeTime': '20:56:23',
        'Beg_Key': '05',
        'Beg_Name': '20:00',
        'End_Key': '05',
        'End_Name': '20:00',
        'Numbers': 2,
        'RmNo': '555',
        'Remark': '6位，中房畅饮'
    },
    {
        'InvNo': 'A02426914',
        'CustName': '贵宾先生',
        'ComeDate': '20250718',
        'ComeTime': '14:02:31',
        'Beg_Key': '02',
        'Beg_Name': '13:30-16:30',
        'End_Key': '02',
        'End_Name': '13:30-16:30',
        'Numbers': 2,
        'RmNo': '666',
        'Remark': ''
    },
    {
        'InvNo': 'A02426911',
        'CustName': '段女士',
        'ComeDate': '20250718',
        'ComeTime': '13:24:23',
        'Beg_Key': '02',
        'Beg_Name': '13:30-16:30',
        'End_Key': '02',
        'End_Name': '13:30-16:30',
        'Numbers': 10,
        'RmNo': '333',
        'Remark': '拾位\n美团1488X1'
    },
    {
        'InvNo': 'A02426816',
        'CustName': '张女士',
        'ComeDate': '20250717',
        'ComeTime': '13:19:49',
        'Beg_Key': '02',
        'Beg_Name': '13:30-16:30',
        'End_Key': '02',
        'End_Name': '13:30-16:30',
        'Numbers': 2,
        'RmNo': '313',
        'Remark': '贰位 V返卡扣'
    },
    {
        'InvNo': 'A02426904',
        'CustName': '贵宾女士',
        'ComeDate': '20250718',
        'ComeTime': '13:20:02',
        'Beg_Key': '02',
        'Beg_Name': '13:30-16:30',
        'End_Key': '02',
        'End_Name': '13:30-16:30',
        'Numbers': 2,
        'RmNo': '302',
        'Remark': '贰位\n在线团购x2'
    },
    {
        'InvNo': 'A02426908',
        'CustName': '刘宇轩先生',
        'ComeDate': '20250718',
        'ComeTime': '13:21:50',
        'Beg_Key': '02',
        'Beg_Name': '13:30-16:30',
        'End_Key': '02',
        'End_Name': '13:30-16:30',
        'Numbers': 5,
        'RmNo': '806',
        'Remark': '提前购券，同意使用'
    },
    {
        'InvNo': 'A02426850',
        'CustName': '吴先生',
        'ComeDate': '20250717',
        'ComeTime': '17:52:59',
        'Beg_Key': '37',
        'Beg_Name': '18:00-21:00',
        'End_Key': '37',
        'End_Name': '18:00-21:00',
        'Numbers': 2,
        'RmNo': '806',
        'Remark': '贰位，V返'
    },
    {
        'InvNo': 'A02426895',
        'CustName': '李女士',
        'ComeDate': '20250718',
        'ComeTime': '11:40:46',
        'Beg_Key': '28',
        'Beg_Name': '11:50-14:50',
        'End_Key': '28',
        'End_Name': '11:50-14:50',
        'Numbers': 2,
        'RmNo': '319',
        'Remark': '44010519900718xxxx\n贰位\n寿星免一✖️1\nV返'
    }
]

def analyze_sample_data():
    print("📊 开台数据样本分析")
    print("=" * 80)
    
    # 按时间段分组统计
    time_slot_stats = {}
    direct_fall_orders = []
    single_slot_orders = []
    
    for order in sample_data:
        # 判断是否直落
        if order['Beg_Name'] != order['End_Name']:
            direct_fall_orders.append(order)
            order_type = "直落"
        else:
            single_slot_orders.append(order)
            order_type = "单时间段"
        
        # 统计时间段
        time_slot = order['Beg_Name']
        if time_slot not in time_slot_stats:
            time_slot_stats[time_slot] = {
                'orders': [],
                'total_people': 0,
                'direct_fall_count': 0,
                'single_slot_count': 0
            }
        
        time_slot_stats[time_slot]['orders'].append(order)
        time_slot_stats[time_slot]['total_people'] += order['Numbers']
        
        if order_type == "直落":
            time_slot_stats[time_slot]['direct_fall_count'] += 1
        else:
            time_slot_stats[time_slot]['single_slot_count'] += 1
    
    # 输出统计结果
    print(f"\n📈 总体统计:")
    print(f"  总订单数: {len(sample_data)}")
    print(f"  直落订单: {len(direct_fall_orders)} 个")
    print(f"  单时间段订单: {len(single_slot_orders)} 个")
    print(f"  总人数: {sum(order['Numbers'] for order in sample_data)} 人")
    
    print(f"\n⏰ 时间段分布:")
    print("-" * 80)
    print(f"{'时间段':<15} {'订单数':<8} {'人数':<8} {'直落':<6} {'单时间段':<8} {'日期分布'}")
    print("-" * 80)
    
    for time_slot, stats in sorted(time_slot_stats.items()):
        # 统计日期分布
        dates = set(order['ComeDate'] for order in stats['orders'])
        date_str = ', '.join(sorted(dates))
        
        print(f"{time_slot:<15} "
              f"{len(stats['orders']):<8} "
              f"{stats['total_people']:<8} "
              f"{stats['direct_fall_count']:<6} "
              f"{stats['single_slot_count']:<8} "
              f"{date_str}")
    
    print(f"\n📋 详细订单信息:")
    print("=" * 120)
    print(f"{'订单号':<12} {'客户':<15} {'日期':<10} {'开台时间':<10} {'时间段':<15} {'人数':<4} {'房间':<6} {'类型':<8} {'备注'}")
    print("-" * 120)
    
    for order in sample_data:
        order_type = "直落" if order['Beg_Name'] != order['End_Name'] else "单时间段"
        remark = order['Remark'].replace('\n', ' ')[:20] + "..." if len(order['Remark']) > 20 else order['Remark']
        
        print(f"{order['InvNo']:<12} "
              f"{order['CustName']:<15} "
              f"{order['ComeDate']:<10} "
              f"{order['ComeTime']:<10} "
              f"{order['Beg_Name']:<15} "
              f"{order['Numbers']:<4} "
              f"{order['RmNo']:<6} "
              f"{order_type:<8} "
              f"{remark}")
    
    # 渠道分析（基于备注）
    print(f"\n📱 渠道分析（基于备注）:")
    print("-" * 60)
    
    channel_stats = {}
    for order in sample_data:
        remark = order['Remark'].lower()
        if '美团' in remark:
            channel = '美团'
        elif '在线团购' in remark:
            channel = '在线团购'
        elif 'v返' in remark:
            channel = 'V返卡'
        elif '生日' in remark or '寿星' in remark:
            channel = '生日优惠'
        else:
            channel = 'K+'
        
        if channel not in channel_stats:
            channel_stats[channel] = {'count': 0, 'people': 0}
        
        channel_stats[channel]['count'] += 1
        channel_stats[channel]['people'] += order['Numbers']
    
    for channel, stats in sorted(channel_stats.items()):
        print(f"  {channel}: {stats['count']}单, {stats['people']}人")
    
    # 特殊情况分析
    print(f"\n🔍 特殊情况分析:")
    print("-" * 60)
    
    # 大团体订单
    large_groups = [order for order in sample_data if order['Numbers'] >= 5]
    if large_groups:
        print(f"  大团体订单（5人以上）: {len(large_groups)}单")
        for order in large_groups:
            print(f"    {order['InvNo']} - {order['CustName']} - {order['Numbers']}人")
    
    # 跨日期订单
    dates = set(order['ComeDate'] for order in sample_data)
    if len(dates) > 1:
        print(f"  涉及日期: {', '.join(sorted(dates))}")
        for date in sorted(dates):
            date_orders = [order for order in sample_data if order['ComeDate'] == date]
            print(f"    {date}: {len(date_orders)}单")
    
    # 晚到订单（开台时间晚于时间段开始时间）
    late_arrivals = []
    for order in sample_data:
        come_time = order['ComeTime']
        time_slot = order['Beg_Name']
        
        # 简单判断（这里可以更精确）
        if '11:50-14:50' in time_slot and come_time < '11:50:00':
            late_arrivals.append(order)
        elif '13:30-16:30' in time_slot and come_time < '13:30:00':
            late_arrivals.append(order)
        elif '18:00-21:00' in time_slot and come_time < '18:00:00':
            late_arrivals.append(order)
        elif '19:00-22:00' in time_slot and come_time < '19:00:00':
            late_arrivals.append(order)
    
    if late_arrivals:
        print(f"  提前到达订单: {len(late_arrivals)}单")
        for order in late_arrivals:
            print(f"    {order['InvNo']} - {order['ComeTime']} 到达 {order['Beg_Name']} 时间段")

if __name__ == "__main__":
    analyze_sample_data()
