#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的KTV数据分析脚本
"""

import pyodbc
import pandas as pd
from datetime import datetime, timedelta
import json

def get_connection(server, database, username, password):
    """获取数据库连接"""
    conn_str = f"""
    DRIVER={{ODBC Driver 17 for SQL Server}};
    SERVER={server};
    DATABASE={database};
    UID={username};
    PWD={password};
    """
    return pyodbc.connect(conn_str)

def analyze_ktv_data(work_date='20250717'):
    """分析KTV数据"""
    print(f"=== 分析日期: {work_date} ===")
    
    # 获取结账数据
    try:
        conn = get_connection('192.168.2.5', 'operatedata', 'sa', 'Musicbox123')
        
        query = f"""
        SELECT Ikey, Shopid, InvNo, WorkDate, CloseDatetime, Tot, VesaName
        FROM rmcloseinfo 
        WHERE shopid = 11 AND WorkDate = '{work_date}'
        ORDER BY CloseDatetime
        """
        
        close_df = pd.read_sql(query, conn)
        conn.close()
        
        print(f"结账记录: {len(close_df)} 条")
        
        if len(close_df) == 0:
            print("没有找到结账数据")
            return
        
        # 基本统计
        total_revenue = close_df['Tot'].sum()
        total_orders = len(close_df)
        
        print(f"总营业额: ¥{total_revenue:,}")
        print(f"总订单数: {total_orders}")
        
        # 按小时统计
        close_df['Hour'] = pd.to_datetime(close_df['CloseDatetime']).dt.hour
        hourly_stats = close_df.groupby('Hour').agg({
            'Tot': ['sum', 'count'],
            'InvNo': 'count'
        }).round(2)
        
        print("\n=== 按小时统计 ===")
        print(hourly_stats)
        
        # 渠道分析
        close_df['Channel'] = close_df['VesaName'].fillna('K+')
        channel_stats = close_df.groupby('Channel').agg({
            'Tot': ['sum', 'count'],
            'InvNo': 'count'
        }).round(2)
        
        print("\n=== 渠道分析 ===")
        print(channel_stats)
        
        # 获取时间段配置
        try:
            conn = get_connection('193.112.2.229', 'rms2019', 'sa', 'Musicbox@123')
            
            time_query = """
            SELECT st.*, t.TimeName, t.BegTime, t.EndTime
            FROM shoptimeinfo st
            LEFT JOIN timeinfo t ON st.timeno = t.timeno
            WHERE st.shopid = 11
            ORDER BY t.BegTime
            """
            
            time_df = pd.read_sql(time_query, conn)
            conn.close()
            
            print(f"\n=== 时间段配置 ({len(time_df)} 个) ===")
            for _, row in time_df.iterrows():
                if pd.notna(row['BegTime']) and pd.notna(row['EndTime']):
                    print(f"{row['TimeName']}: {row['BegTime']:04d}-{row['EndTime']:04d}")
            
        except Exception as e:
            print(f"获取时间段配置失败: {e}")
        
        # 保存详细数据
        result = {
            'work_date': work_date,
            'total_revenue': int(total_revenue),
            'total_orders': total_orders,
            'hourly_summary': f"按小时统计完成，共{len(hourly_stats)}个小时有数据",
            'channel_summary': f"渠道分析完成，共{len(channel_stats)}个渠道",
            'sample_data': close_df[['InvNo', 'CloseDatetime', 'Tot', 'VesaName']].head(10).to_dict('records')
        }
        
        with open(f'ktv_analysis_{work_date}.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n详细数据已保存到: ktv_analysis_{work_date}.json")
        
        # 显示前几条记录
        print("\n=== 样本数据 ===")
        print(close_df[['InvNo', 'CloseDatetime', 'Tot', 'VesaName']].head(10))
        
    except Exception as e:
        print(f"分析失败: {e}")

def main():
    # 分析多个日期
    dates = ['20250717', '20250716', '20250715']
    
    for date in dates:
        try:
            analyze_ktv_data(date)
            print("\n" + "="*60 + "\n")
        except Exception as e:
            print(f"分析日期 {date} 失败: {e}")

if __name__ == "__main__":
    main()
