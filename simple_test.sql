-- 简化测试：验证夜间档细化分类逻辑
USE OperateData;
GO

-- 验证夜间档细化分类的效果
SELECT 
    '夜间档分类统计' AS 统计类型,
    CASE 
        WHEN rt.CtNo = 19 THEN '自由餐'
        WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%买断%') THEN '啤酒买断'
        WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%畅饮%') THEN '畅饮套餐'
        ELSE '其他非自由餐'
    END AS 消费子类型,
    CASE 
        WHEN rt.MTPay > 0 THEN '美团' 
        WHEN rt.DZPay > 0 THEN '抖音' 
        WHEN rt.AliPay > 0 THEN '特权预约' 
        WHEN rt.CtNo = 2 THEN 'K+' 
        WHEN rt.CtNo = 1 THEN '房费' 
        ELSE '其他' 
    END AS 渠道,
    COUNT(*) AS 订单数量,
    SUM(rt.TotalAmount) AS 总金额
FROM dbo.RmCloseInfo_Test rt
WHERE rt.ShopId = 3 
    AND rt.WorkDate = '20250509' 
    AND rt.OpenDateTime >= DATEADD(hour, 20, CAST('20250509' AS datetime))
GROUP BY 
    CASE 
        WHEN rt.CtNo = 19 THEN '自由餐'
        WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%买断%') THEN '啤酒买断'
        WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%畅饮%') THEN '畅饮套餐'
        ELSE '其他非自由餐'
    END,
    CASE 
        WHEN rt.MTPay > 0 THEN '美团' 
        WHEN rt.DZPay > 0 THEN '抖音' 
        WHEN rt.AliPay > 0 THEN '特权预约' 
        WHEN rt.CtNo = 2 THEN 'K+' 
        WHEN rt.CtNo = 1 THEN '房费' 
        ELSE '其他' 
    END
ORDER BY 消费子类型, 渠道;
GO
