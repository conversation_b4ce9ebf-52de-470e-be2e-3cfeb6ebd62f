-- 测试增强版分类逻辑（包含自由消套餐）
USE OperateData;
GO

-- 测试1: 验证夜间档分类逻辑（包含自由消套餐）
PRINT '=== 测试夜间档分类逻辑（包含自由消套餐） ===';
DECLARE @TestShopId int = 3;
DECLARE @TestWorkDate varchar(8) = '20250502';

WITH NightOrderClassifications AS (
    SELECT
        rt.InvNo,
        rt.TotalAmount,
        rt.CtNo,
        rt.MTPay,
        rt.DZPay,
        rt.AliPay,
        MAX(CASE WHEN fcb.FdCName LIKE N'%买断%' THEN 1 ELSE 0 END) AS HasBuyout,
        MAX(CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN 1 ELSE 0 END) AS HasChangyin,
        MAX(CASE WHEN fcb.FdCName LIKE N'%自由消%' THEN 1 ELSE 0 END) AS HasFreeConsumption
    FROM dbo.RmCloseInfo_Test AS rt
    LEFT JOIN dbo.FdCashBak AS fcb ON rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
    WHERE rt.ShopId = @TestShopId 
        AND rt.WorkDate = @TestWorkDate
        AND rt.OpenDateTime >= DATEADD(hour, 20, CAST(@TestWorkDate AS datetime))
    GROUP BY rt.InvNo, rt.TotalAmount, rt.CtNo, rt.MTPay, rt.DZPay, rt.AliPay
)
SELECT 
    '夜间档分类验证' AS 测试类型,
    CASE 
        WHEN HasBuyout = 1 THEN '啤酒买断'
        WHEN HasChangyin = 1 THEN '畅饮套餐'
        WHEN HasFreeConsumption = 1 THEN '自由消套餐'
        WHEN CtNo = 19 THEN '自由餐'
        ELSE '其他非自由餐'
    END AS 消费类型,
    CASE 
        WHEN MTPay > 0 THEN '美团'
        WHEN DZPay > 0 THEN '抖音'
        WHEN AliPay > 0 THEN '特权预约'
        WHEN CtNo = 2 THEN 'K+'
        WHEN CtNo = 1 THEN '房费'
        ELSE '其他'
    END AS 渠道,
    COUNT(*) AS 订单数量,
    SUM(TotalAmount) AS 总金额
FROM NightOrderClassifications
GROUP BY 
    CASE 
        WHEN HasBuyout = 1 THEN '啤酒买断'
        WHEN HasChangyin = 1 THEN '畅饮套餐'
        WHEN HasFreeConsumption = 1 THEN '自由消套餐'
        WHEN CtNo = 19 THEN '自由餐'
        ELSE '其他非自由餐'
    END,
    CASE 
        WHEN MTPay > 0 THEN '美团'
        WHEN DZPay > 0 THEN '抖音'
        WHEN AliPay > 0 THEN '特权预约'
        WHEN CtNo = 2 THEN 'K+'
        WHEN CtNo = 1 THEN '房费'
        ELSE '其他'
    END
ORDER BY 消费类型, 渠道;

-- 测试2: 验证优先级逻辑
PRINT '=== 测试优先级逻辑 ===';
WITH TestPriorityLogic AS (
    SELECT
        rt.InvNo,
        MAX(CASE WHEN fcb.FdCName LIKE N'%买断%' THEN 1 ELSE 0 END) AS HasBuyout,
        MAX(CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN 1 ELSE 0 END) AS HasChangyin,
        MAX(CASE WHEN fcb.FdCName LIKE N'%自由消%' THEN 1 ELSE 0 END) AS HasFreeConsumption,
        rt.CtNo
    FROM dbo.RmCloseInfo_Test AS rt
    LEFT JOIN dbo.FdCashBak AS fcb ON rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
    WHERE rt.ShopId = @TestShopId 
        AND rt.WorkDate = @TestWorkDate
        AND rt.OpenDateTime >= DATEADD(hour, 20, CAST(@TestWorkDate AS datetime))
    GROUP BY rt.InvNo, rt.CtNo
)
SELECT 
    '优先级验证' AS 测试类型,
    HasBuyout AS 有买断,
    HasChangyin AS 有畅饮,
    HasFreeConsumption AS 有自由消,
    CASE 
        WHEN HasBuyout = 1 THEN '啤酒买断'
        WHEN HasChangyin = 1 THEN '畅饮套餐'
        WHEN HasFreeConsumption = 1 THEN '自由消套餐'
        WHEN CtNo = 19 THEN '自由餐'
        ELSE '其他非自由餐'
    END AS 最终分类,
    COUNT(*) AS 订单数量
FROM TestPriorityLogic
GROUP BY HasBuyout, HasChangyin, HasFreeConsumption, CtNo,
    CASE 
        WHEN HasBuyout = 1 THEN '啤酒买断'
        WHEN HasChangyin = 1 THEN '畅饮套餐'
        WHEN HasFreeConsumption = 1 THEN '自由消套餐'
        WHEN CtNo = 19 THEN '自由餐'
        ELSE '其他非自由餐'
    END
ORDER BY 最终分类;

-- 测试3: 检查是否有同时包含多种类型的订单
PRINT '=== 检查混合类型订单 ===';
SELECT 
    '混合类型检查' AS 测试类型,
    rt.InvNo,
    SUM(CASE WHEN fcb.FdCName LIKE N'%买断%' THEN 1 ELSE 0 END) AS 买断商品数,
    SUM(CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN 1 ELSE 0 END) AS 畅饮商品数,
    SUM(CASE WHEN fcb.FdCName LIKE N'%自由消%' THEN 1 ELSE 0 END) AS 自由消商品数,
    COUNT(fcb.FdCName) AS 总商品数
FROM dbo.RmCloseInfo_Test AS rt
JOIN dbo.FdCashBak AS fcb ON rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
WHERE rt.ShopId = @TestShopId 
    AND rt.WorkDate = @TestWorkDate
    AND rt.OpenDateTime >= DATEADD(hour, 20, CAST(@TestWorkDate AS datetime))
    AND (fcb.FdCName LIKE N'%买断%' OR fcb.FdCName LIKE N'%畅饮%' OR fcb.FdCName LIKE N'%自由消%')
GROUP BY rt.InvNo
HAVING (SUM(CASE WHEN fcb.FdCName LIKE N'%买断%' THEN 1 ELSE 0 END) > 0 AND 
        SUM(CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN 1 ELSE 0 END) > 0) OR
       (SUM(CASE WHEN fcb.FdCName LIKE N'%买断%' THEN 1 ELSE 0 END) > 0 AND 
        SUM(CASE WHEN fcb.FdCName LIKE N'%自由消%' THEN 1 ELSE 0 END) > 0) OR
       (SUM(CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN 1 ELSE 0 END) > 0 AND 
        SUM(CASE WHEN fcb.FdCName LIKE N'%自由消%' THEN 1 ELSE 0 END) > 0)
ORDER BY rt.InvNo;

-- 测试4: 验证存储过程输出的数据一致性
PRINT '=== 验证存储过程数据一致性 ===';
SELECT 
    '数据一致性验证' AS 测试类型,
    COUNT(*) AS 夜间档总订单数,
    SUM(CASE WHEN rt.CtNo = 19 THEN 1 ELSE 0 END) AS 自由餐订单数,
    SUM(CASE WHEN rt.CtNo <> 19 THEN 1 ELSE 0 END) AS 非自由餐订单数
FROM dbo.RmCloseInfo_Test AS rt
WHERE rt.ShopId = @TestShopId 
    AND rt.WorkDate = @TestWorkDate
    AND rt.OpenDateTime >= DATEADD(hour, 20, CAST(@TestWorkDate AS datetime));

GO
