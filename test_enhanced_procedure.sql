-- 测试优化后的存储过程
-- 连接到 OperateData 数据库
USE OperateData;
GO

-- 测试天河店 2025年5月9日的数据
EXEC dbo.usp_GenerateFullDailyReport_Enhanced 
    @ShopId = 3,
    @BeginDate = '2025-05-09',
    @EndDate = '2025-05-09';
GO

-- 验证夜间档细化分类的效果
-- 查看原始数据分类情况
SELECT 
    '原始数据验证' AS 验证类型,
    CASE 
        WHEN rt.CtNo = 19 THEN '自由餐'
        WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%买断%') THEN '啤酒买断'
        WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%畅饮%') THEN '畅饮套餐'
        ELSE '其他非自由餐'
    END AS 消费子类型,
    CASE 
        WHEN rt.MTPay > 0 THEN '美团' 
        WHEN rt.DZPay > 0 THEN '抖音' 
        WHEN rt.AliPay > 0 THEN '特权预约' 
        WHEN rt.CtNo = 2 THEN 'K+' 
        WHEN rt.CtNo = 1 THEN '房费' 
        ELSE '其他' 
    END AS 渠道,
    COUNT(*) AS 订单数量,
    SUM(rt.TotalAmount) AS 总金额
FROM dbo.RmCloseInfo_Test rt
WHERE rt.ShopId = 3 
    AND rt.WorkDate = '20250509' 
    AND rt.OpenDateTime >= DATEADD(hour, 20, CAST('20250509' AS datetime))
GROUP BY 
    CASE 
        WHEN rt.CtNo = 19 THEN '自由餐'
        WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%买断%') THEN '啤酒买断'
        WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE N'%畅饮%') THEN '畅饮套餐'
        ELSE '其他非自由餐'
    END,
    CASE 
        WHEN rt.MTPay > 0 THEN '美团' 
        WHEN rt.DZPay > 0 THEN '抖音' 
        WHEN rt.AliPay > 0 THEN '特权预约' 
        WHEN rt.CtNo = 2 THEN 'K+' 
        WHEN rt.CtNo = 1 THEN '房费' 
        ELSE '其他' 
    END
ORDER BY 消费子类型, 渠道;
GO

-- 查看具体的买断和畅饮订单示例
SELECT TOP 10
    '买断订单示例' AS 类型,
    rt.InvNo,
    rt.OpenDateTime,
    rt.TotalAmount,
    fcb.FdCName,
    fcb.FdPrice,
    CASE 
        WHEN rt.MTPay > 0 THEN '美团' 
        WHEN rt.DZPay > 0 THEN '抖音' 
        WHEN rt.AliPay > 0 THEN '特权预约' 
        WHEN rt.CtNo = 2 THEN 'K+' 
        WHEN rt.CtNo = 1 THEN '房费' 
        ELSE '其他' 
    END AS 渠道
FROM dbo.RmCloseInfo_Test rt
JOIN dbo.FdCashBak fcb ON rt.InvNo = fcb.InvNo
WHERE rt.ShopId = 3 
    AND rt.WorkDate = '20250509' 
    AND rt.OpenDateTime >= DATEADD(hour, 20, CAST('20250509' AS datetime))
    AND fcb.FdCName LIKE N'%买断%'
ORDER BY rt.OpenDateTime;
GO

SELECT TOP 10
    '畅饮订单示例' AS 类型,
    rt.InvNo,
    rt.OpenDateTime,
    rt.TotalAmount,
    fcb.FdCName,
    fcb.FdPrice,
    CASE 
        WHEN rt.MTPay > 0 THEN '美团' 
        WHEN rt.DZPay > 0 THEN '抖音' 
        WHEN rt.AliPay > 0 THEN '特权预约' 
        WHEN rt.CtNo = 2 THEN 'K+' 
        WHEN rt.CtNo = 1 THEN '房费' 
        ELSE '其他' 
    END AS 渠道
FROM dbo.RmCloseInfo_Test rt
JOIN dbo.FdCashBak fcb ON rt.InvNo = fcb.InvNo
WHERE rt.ShopId = 3 
    AND rt.WorkDate = '20250509' 
    AND rt.OpenDateTime >= DATEADD(hour, 20, CAST('20250509' AS datetime))
    AND fcb.FdCName LIKE N'%畅饮%'
    AND NOT EXISTS(SELECT 1 FROM dbo.FdCashBak fcb2 WHERE fcb2.InvNo = rt.InvNo AND fcb2.FdCName LIKE N'%买断%')
ORDER BY rt.OpenDateTime;
GO
