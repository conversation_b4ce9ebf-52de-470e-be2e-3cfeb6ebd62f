-- 测试夜间档细化分类的核心逻辑
USE OperateData;
GO

-- 创建测试存储过程
IF OBJECT_ID('dbo.usp_TestNightClassification', 'P') IS NOT NULL
DROP PROCEDURE dbo.usp_TestNightClassification;
GO

CREATE PROCEDURE dbo.usp_TestNightClassification
    @ShopId int = 3,
    @WorkDate varchar(8) = '20250509'
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 夜间档细化分类统计
    SELECT
        WorkDate,
        -- 啤酒买断统计
        SUM(CASE WHEN ConsumptionSubType = 'BeerBuyout' AND Channel = 'KPlus' THEN 1 ELSE 0 END) AS Night_BeerBuyout_KPlus,
        SUM(CASE WHEN ConsumptionSubType = 'BeerBuyout' AND Channel = 'Special' THEN 1 ELSE 0 END) AS Night_BeerBuyout_Special,
        SUM(CASE WHEN ConsumptionSubType = 'BeerBuyout' AND Channel = 'Meituan' THEN 1 ELSE 0 END) AS Night_BeerBuyout_Meitu<PERSON>,
        SUM(CASE WHEN ConsumptionSubType = 'BeerBuyout' AND Channel = 'Douyin' THEN 1 ELSE 0 END) AS Night_BeerBuyout_Douyin,
        SUM(CASE WHEN ConsumptionSubType = 'BeerBuyout' AND Channel = 'RoomFee' THEN 1 ELSE 0 END) AS Night_BeerBuyout_RoomFee,
        SUM(CASE WHEN ConsumptionSubType = 'BeerBuyout' AND Channel = 'Others' THEN 1 ELSE 0 END) AS Night_BeerBuyout_Others,
        SUM(CASE WHEN ConsumptionSubType = 'BeerBuyout' THEN 1 ELSE 0 END) AS Night_BeerBuyout_Subtotal,
        SUM(CASE WHEN ConsumptionSubType = 'BeerBuyout' THEN TotalAmount ELSE 0 END) AS Night_BeerBuyout_Revenue,
        
        -- 畅饮套餐统计
        SUM(CASE WHEN ConsumptionSubType = 'DrinkPackage' AND Channel = 'KPlus' THEN 1 ELSE 0 END) AS Night_DrinkPackage_KPlus,
        SUM(CASE WHEN ConsumptionSubType = 'DrinkPackage' AND Channel = 'Special' THEN 1 ELSE 0 END) AS Night_DrinkPackage_Special,
        SUM(CASE WHEN ConsumptionSubType = 'DrinkPackage' AND Channel = 'Meituan' THEN 1 ELSE 0 END) AS Night_DrinkPackage_Meituan,
        SUM(CASE WHEN ConsumptionSubType = 'DrinkPackage' AND Channel = 'Douyin' THEN 1 ELSE 0 END) AS Night_DrinkPackage_Douyin,
        SUM(CASE WHEN ConsumptionSubType = 'DrinkPackage' AND Channel = 'RoomFee' THEN 1 ELSE 0 END) AS Night_DrinkPackage_RoomFee,
        SUM(CASE WHEN ConsumptionSubType = 'DrinkPackage' AND Channel = 'Others' THEN 1 ELSE 0 END) AS Night_DrinkPackage_Others,
        SUM(CASE WHEN ConsumptionSubType = 'DrinkPackage' THEN 1 ELSE 0 END) AS Night_DrinkPackage_Subtotal,
        SUM(CASE WHEN ConsumptionSubType = 'DrinkPackage' THEN TotalAmount ELSE 0 END) AS Night_DrinkPackage_Revenue,
        
        -- 其他非自由餐统计
        SUM(CASE WHEN ConsumptionSubType = 'OtherNonFree' AND Channel = 'KPlus' THEN 1 ELSE 0 END) AS Night_OtherNonFree_KPlus,
        SUM(CASE WHEN ConsumptionSubType = 'OtherNonFree' AND Channel = 'Special' THEN 1 ELSE 0 END) AS Night_OtherNonFree_Special,
        SUM(CASE WHEN ConsumptionSubType = 'OtherNonFree' AND Channel = 'Meituan' THEN 1 ELSE 0 END) AS Night_OtherNonFree_Meituan,
        SUM(CASE WHEN ConsumptionSubType = 'OtherNonFree' AND Channel = 'Douyin' THEN 1 ELSE 0 END) AS Night_OtherNonFree_Douyin,
        SUM(CASE WHEN ConsumptionSubType = 'OtherNonFree' AND Channel = 'RoomFee' THEN 1 ELSE 0 END) AS Night_OtherNonFree_RoomFee,
        SUM(CASE WHEN ConsumptionSubType = 'OtherNonFree' AND Channel = 'Others' THEN 1 ELSE 0 END) AS Night_OtherNonFree_Others,
        SUM(CASE WHEN ConsumptionSubType = 'OtherNonFree' THEN 1 ELSE 0 END) AS Night_OtherNonFree_Subtotal,
        SUM(CASE WHEN ConsumptionSubType = 'OtherNonFree' THEN TotalAmount ELSE 0 END) AS Night_OtherNonFree_Revenue
    FROM (
        SELECT 
            rt.*,
            -- 渠道分类
            CASE 
                WHEN rt.MTPay > 0 THEN 'Meituan' 
                WHEN rt.DZPay > 0 THEN 'Douyin' 
                WHEN rt.AliPay > 0 THEN 'Special' 
                WHEN rt.CtNo = 2 THEN 'KPlus' 
                WHEN rt.CtNo = 1 THEN 'RoomFee' 
                ELSE 'Others' 
            END AS Channel,
            -- 细化的非自由餐子类型分类（优先级：买断 > 畅饮 > 其他）
            CASE 
                WHEN rt.CtNo = 19 THEN 'FreeMeal'
                WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE '%买断%') THEN 'BeerBuyout'
                WHEN EXISTS(SELECT 1 FROM dbo.FdCashBak fcb WHERE fcb.InvNo = rt.InvNo AND fcb.FdCName LIKE '%畅饮%') THEN 'DrinkPackage'
                ELSE 'OtherNonFree'
            END AS ConsumptionSubType
        FROM dbo.RmCloseInfo_Test rt
        WHERE rt.ShopId = @ShopId
            AND rt.WorkDate = @WorkDate
            AND rt.OpenDateTime >= DATEADD(hour, 20, CAST(@WorkDate AS datetime))
            AND rt.CtNo <> 19  -- 排除自由餐
    ) AS NightShiftData
    GROUP BY WorkDate;
END
GO
