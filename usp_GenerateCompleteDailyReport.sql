-- 连接到 OperateData 数据库
USE OperateData;
GO

-- 设置必要的选项
SET QUOTED_IDENTIFIER ON;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateCompleteDailyReport', 'P') IS NOT NULL
DROP PROCEDURE dbo.usp_GenerateCompleteDailyReport;
GO

CREATE PROCEDURE dbo.usp_GenerateCompleteDailyReport
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    -- ====================================================================
    -- 步骤 1: 参数处理
    -- ====================================================================
    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    -- ====================================================================
    -- 步骤 2: 动态构建白天分时段统计的列（包含上一档直落）
    -- ====================================================================
    DECLARE @PivotColumns nvarchar(MAX) = '';
    DECLARE @PivotSelectColumns nvarchar(MAX) = '';

    -- 构建动态列定义
    SELECT @PivotColumns = STUFF((
        SELECT
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.CtNo = 2 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_K+') +
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.AliPay > 0 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_特权预约') +
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.MTPay > 0 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_美团') +
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.DZPay > 0 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_抖音') +
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' AND rt.CtNo = 1 THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_房费') +
            ', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + ' THEN 1 ELSE 0 END), 0) AS ' + QUOTENAME(t.TimeName + '_小计') +
            ', ISNULL((SELECT COUNT(tdi.InvNo) FROM TrueDropInData AS tdi JOIN TimeSlots ts_beg ON tdi.Beg_Key = ts_beg.TimeNo WHERE ts_beg.BegTime < ' + CAST(t.BegTime AS varchar) + ' AND tdi.CloseDatetime > ts_main.NextSlotStartDateTime), 0) AS ' + QUOTENAME(t.TimeName + '_上一档直落')
        FROM (
            SELECT DISTINCT ti.TimeName, ti.BegTime
            FROM dbo.shoptimeinfo sti
            JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
            WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000
        ) AS t
        ORDER BY BegTime
        FOR XML PATH(''), TYPE
    ).value('.', 'nvarchar(MAX)'), 1, 1, '');

    -- 构建SELECT列
    SELECT @PivotSelectColumns = STUFF((
        SELECT ',' + QUOTENAME(t.TimeName + '_K+') + ',' + QUOTENAME(t.TimeName + '_特权预约') + ',' + QUOTENAME(t.TimeName + '_美团') + ',' + QUOTENAME(t.TimeName + '_抖音') + ',' + QUOTENAME(t.TimeName + '_房费') + ',' + QUOTENAME(t.TimeName + '_小计') + ',' + QUOTENAME(t.TimeName + '_上一档直落')
        FROM (SELECT DISTINCT ti.TimeName, ti.BegTime FROM dbo.shoptimeinfo sti JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000) AS t
        ORDER BY BegTime FOR XML PATH('')
    ), 1, 1, '');

    -- ====================================================================
    -- 步骤 3: 构建并执行完整的动态 SQL 查询
    -- ====================================================================
    DECLARE @DynamicSQL nvarchar(MAX);

    SET @DynamicSQL = N'
    -- CTE 1: 预处理总览和直落指标
    WITH OverviewAndDropInData AS (
        SELECT
            rt.WorkDate, b.ShopName, DATENAME(weekday, CAST(rt.WorkDate AS date)) AS WeekdayName,
            SUM(rt.TotalAmount) AS TotalRevenue, 
            SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END) AS DayTimeRevenue, 
            SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END) AS NightTimeRevenue,
            COUNT(rt.InvNo) AS TotalBatchCount, 
            COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS DayTimeBatchCount, 
            COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
            SUM(rt.Numbers) AS TotalGuestCount, 
            SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END) AS BuffetGuestCount,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END) AS DayTimeDropInBatch,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END) AS NightTimeDropInBatch,
            SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END) AS TotalDropInGuests
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN MIMS.dbo.ShopInfo AS b ON rt.ShopId = b.Shopid
        LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
        LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
        LEFT JOIN dbo.timeinfo AS ti_end ON sti_end.TimeNo = ti_end.TimeNo
        WHERE rt.ShopId = @ShopId_Param AND CAST(rt.WorkDate AS date) >= @BeginDate_Param AND CAST(rt.WorkDate AS date) <= @EndDate_Param AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.WorkDate, b.ShopName
    ),
    -- CTE 2: 时间档和直落数据预处理
    TimeSlots AS (
        SELECT
            ti.TimeNo, ti.TimeName, ti.BegTime,
            DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(CAST(rt.WorkDate AS date) AS datetime))) AS SlotStartDateTime,
            LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(CAST(rt.WorkDate AS date) AS datetime))), 1, ''2999-12-31'') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
        FROM dbo.shoptimeinfo AS sti
        JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo
        CROSS JOIN (SELECT DISTINCT WorkDate FROM dbo.RmCloseInfo_Test WHERE ShopId = @ShopId_Param AND CAST(WorkDate AS date) >= @BeginDate_Param AND CAST(WorkDate AS date) <= @EndDate_Param) AS rt
        WHERE sti.ShopId = @ShopId_Param AND ti.BegTime < 2000
    ),
    TrueDropInData AS (
        SELECT 
            rt.WorkDate, rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key, rt.Numbers,
            ti_beg.BegTime AS BegSlotTime
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
        JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
        JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
        WHERE rt.ShopId = @ShopId_Param 
            AND CAST(rt.WorkDate AS date) >= @BeginDate_Param 
            AND CAST(rt.WorkDate AS date) <= @EndDate_Param 
            AND rt.OpenDateTime IS NOT NULL
            AND rt.Beg_Key <> rt.End_Key
            AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
            AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
    ),
    -- CTE 3: 预处理白天分时段指标（优化版本）
    DayTimePivotedData AS (
        SELECT rt.WorkDate';

    IF ISNULL(@PivotColumns, '') <> ''
    BEGIN
        SET @DynamicSQL = @DynamicSQL + ', ' + @PivotColumns;
    END

    SET @DynamicSQL = @DynamicSQL + N'
        FROM dbo.RmCloseInfo_Test AS rt
        JOIN dbo.timeinfo AS ti ON rt.Beg_Key = ti.TimeNo
        LEFT JOIN TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo AND rt.WorkDate = CAST(ts_main.SlotStartDateTime AS varchar(8))
        WHERE rt.ShopId = @ShopId_Param AND CAST(rt.WorkDate AS date) >= @BeginDate_Param AND CAST(rt.WorkDate AS date) <= @EndDate_Param AND rt.OpenDateTime IS NOT NULL
        GROUP BY rt.WorkDate
    ),
    -- CTE 4: 夜间档优化分类（基于您的优化思路）
    NightOrderClassifications AS (
        SELECT
            rt.WorkDate,
            rt.InvNo,
            rt.TotalAmount,
            rt.CtNo,
            rt.MTPay,
            rt.DZPay,
            rt.AliPay,
            MAX(CASE WHEN fcb.FdCName LIKE N''%买断%'' THEN 1 ELSE 0 END) AS HasBuyout,
            MAX(CASE WHEN fcb.FdCName LIKE N''%畅饮%'' THEN 1 ELSE 0 END) AS HasChangyin
        FROM dbo.RmCloseInfo_Test AS rt
        LEFT JOIN dbo.FdCashBak AS fcb ON rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
        WHERE rt.ShopId = @ShopId_Param 
            AND CAST(rt.WorkDate AS date) >= @BeginDate_Param 
            AND CAST(rt.WorkDate AS date) <= @EndDate_Param
            AND rt.OpenDateTime >= DATEADD(hour, 20, CAST(CAST(rt.WorkDate AS date) AS datetime))
        GROUP BY rt.WorkDate, rt.InvNo, rt.TotalAmount, rt.CtNo, rt.MTPay, rt.DZPay, rt.AliPay
    ),
    -- CTE 5: 夜间档统计汇总
    NightTimeDetailData AS (
        SELECT
            WorkDate,
            -- 自由餐统计
            ISNULL(SUM(CASE WHEN CtNo = 19 AND CtNo = 2 THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_KPlus,
            ISNULL(SUM(CASE WHEN CtNo = 19 AND AliPay > 0 THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Special,
            ISNULL(SUM(CASE WHEN CtNo = 19 AND MTPay > 0 THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Meituan,
            ISNULL(SUM(CASE WHEN CtNo = 19 AND DZPay > 0 THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Douyin,
            ISNULL(SUM(CASE WHEN CtNo = 19 THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Subtotal,
            ISNULL(SUM(CASE WHEN CtNo = 19 THEN TotalAmount ELSE 0 END), 0) AS Night_FreeMeal_Amount,
            -- 啤酒买断统计
            ISNULL(SUM(CASE WHEN HasBuyout = 1 AND CtNo = 2 THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_KPlus,
            ISNULL(SUM(CASE WHEN HasBuyout = 1 AND AliPay > 0 THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Special,
            ISNULL(SUM(CASE WHEN HasBuyout = 1 AND MTPay > 0 THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Meituan,
            ISNULL(SUM(CASE WHEN HasBuyout = 1 AND DZPay > 0 THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Douyin,
            ISNULL(SUM(CASE WHEN HasBuyout = 1 AND CtNo = 1 THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_RoomFee,
            ISNULL(SUM(CASE WHEN HasBuyout = 1 AND CtNo NOT IN (1,2) AND MTPay = 0 AND DZPay = 0 AND AliPay = 0 THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Others,
            ISNULL(SUM(CASE WHEN HasBuyout = 1 THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Subtotal,
            ISNULL(SUM(CASE WHEN HasBuyout = 1 THEN TotalAmount ELSE 0 END), 0) AS Night_BeerBuyout_Revenue,
            -- 畅饮套餐统计
            ISNULL(SUM(CASE WHEN HasBuyout = 0 AND HasChangyin = 1 AND CtNo = 2 THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_KPlus,
            ISNULL(SUM(CASE WHEN HasBuyout = 0 AND HasChangyin = 1 AND AliPay > 0 THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Special,
            ISNULL(SUM(CASE WHEN HasBuyout = 0 AND HasChangyin = 1 AND MTPay > 0 THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Meituan,
            ISNULL(SUM(CASE WHEN HasBuyout = 0 AND HasChangyin = 1 AND DZPay > 0 THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Douyin,
            ISNULL(SUM(CASE WHEN HasBuyout = 0 AND HasChangyin = 1 AND CtNo = 1 THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_RoomFee,
            ISNULL(SUM(CASE WHEN HasBuyout = 0 AND HasChangyin = 1 AND CtNo NOT IN (1,2) AND MTPay = 0 AND DZPay = 0 AND AliPay = 0 THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Others,
            ISNULL(SUM(CASE WHEN HasBuyout = 0 AND HasChangyin = 1 THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Subtotal,
            ISNULL(SUM(CASE WHEN HasBuyout = 0 AND HasChangyin = 1 THEN TotalAmount ELSE 0 END), 0) AS Night_DrinkPackage_Revenue,
            -- 其他非自由餐统计
            ISNULL(SUM(CASE WHEN CtNo <> 19 AND HasBuyout = 0 AND HasChangyin = 0 AND CtNo = 2 THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_KPlus,
            ISNULL(SUM(CASE WHEN CtNo <> 19 AND HasBuyout = 0 AND HasChangyin = 0 AND AliPay > 0 THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Special,
            ISNULL(SUM(CASE WHEN CtNo <> 19 AND HasBuyout = 0 AND HasChangyin = 0 AND MTPay > 0 THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Meituan,
            ISNULL(SUM(CASE WHEN CtNo <> 19 AND HasBuyout = 0 AND HasChangyin = 0 AND DZPay > 0 THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Douyin,
            ISNULL(SUM(CASE WHEN CtNo <> 19 AND HasBuyout = 0 AND HasChangyin = 0 AND CtNo = 1 THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_RoomFee,
            ISNULL(SUM(CASE WHEN CtNo <> 19 AND HasBuyout = 0 AND HasChangyin = 0 AND CtNo NOT IN (1,2) AND MTPay = 0 AND DZPay = 0 AND AliPay = 0 THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Others,
            ISNULL(SUM(CASE WHEN CtNo <> 19 AND HasBuyout = 0 AND HasChangyin = 0 THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Subtotal,
            ISNULL(SUM(CASE WHEN CtNo <> 19 AND HasBuyout = 0 AND HasChangyin = 0 THEN TotalAmount ELSE 0 END), 0) AS Night_OtherNonFree_Revenue
        FROM NightOrderClassifications
        GROUP BY WorkDate
    )
    -- 最终联接所有 CTE 的结果
    SELECT
        ovd.WorkDate AS ''日期'', ovd.ShopName AS ''门店'', ovd.WeekdayName AS ''星期'',
        -- 总览指标
        ISNULL(ovd.TotalRevenue, 0) AS ''营收_总收入'', 
        ISNULL(ovd.DayTimeRevenue, 0) AS ''营收_白天档'', 
        ISNULL(ovd.NightTimeRevenue, 0) AS ''营收_晚上档'',
        ISNULL(ovd.TotalBatchCount, 0) AS ''带客_全天总批数'', 
        ISNULL(ovd.DayTimeBatchCount, 0) AS ''带客_白天档_总批次'', 
        ISNULL(ovd.NightTimeBatchCount, 0) AS ''带客_晚上档_总批次'',
        ISNULL(ovd.DayTimeDropInBatch, 0) AS ''带客_白天档_直落'', 
        ISNULL(ovd.NightTimeDropInBatch, 0) AS ''带客_晚上档_直落'',
        ISNULL(ovd.TotalGuestCount, 0) AS ''用餐_总人数'', 
        ISNULL(ovd.BuffetGuestCount, 0) AS ''用餐_自助餐人数'', 
        ISNULL(ovd.TotalDropInGuests, 0) AS ''用餐_直落人数''';

    IF ISNULL(@PivotSelectColumns, '') <> ''
    BEGIN
        SET @DynamicSQL = @DynamicSQL + ', ' + @PivotSelectColumns;
    END

    SET @DynamicSQL = @DynamicSQL + N'
        -- 夜间档细化统计
        , ntd.Night_FreeMeal_KPlus AS ''晚间_自由餐_K+''
        , ntd.Night_FreeMeal_Special AS ''晚间_自由餐_特权预约''
        , ntd.Night_FreeMeal_Meituan AS ''晚间_自由餐_美团''
        , ntd.Night_FreeMeal_Douyin AS ''晚间_自由餐_抖音''
        , ntd.Night_FreeMeal_Subtotal AS ''晚间_自由餐_小计''
        , ntd.Night_FreeMeal_Amount AS ''晚间_自由餐_消费金额''
        , ntd.Night_BeerBuyout_KPlus AS ''晚间_啤酒买断_K+''
        , ntd.Night_BeerBuyout_Special AS ''晚间_啤酒买断_特权预约''
        , ntd.Night_BeerBuyout_Meituan AS ''晚间_啤酒买断_美团''
        , ntd.Night_BeerBuyout_Douyin AS ''晚间_啤酒买断_抖音''
        , ntd.Night_BeerBuyout_RoomFee AS ''晚间_啤酒买断_房费''
        , ntd.Night_BeerBuyout_Others AS ''晚间_啤酒买断_其他''
        , ntd.Night_BeerBuyout_Subtotal AS ''晚间_啤酒买断_小计''
        , ntd.Night_BeerBuyout_Revenue AS ''晚间_啤酒买断_营业额''
        , ntd.Night_DrinkPackage_KPlus AS ''晚间_畅饮套餐_K+''
        , ntd.Night_DrinkPackage_Special AS ''晚间_畅饮套餐_特权预约''
        , ntd.Night_DrinkPackage_Meituan AS ''晚间_畅饮套餐_美团''
        , ntd.Night_DrinkPackage_Douyin AS ''晚间_畅饮套餐_抖音''
        , ntd.Night_DrinkPackage_RoomFee AS ''晚间_畅饮套餐_房费''
        , ntd.Night_DrinkPackage_Others AS ''晚间_畅饮套餐_其他''
        , ntd.Night_DrinkPackage_Subtotal AS ''晚间_畅饮套餐_小计''
        , ntd.Night_DrinkPackage_Revenue AS ''晚间_畅饮套餐_营业额''
        , ntd.Night_OtherNonFree_KPlus AS ''晚间_其他非自由餐_K+''
        , ntd.Night_OtherNonFree_Special AS ''晚间_其他非自由餐_特权预约''
        , ntd.Night_OtherNonFree_Meituan AS ''晚间_其他非自由餐_美团''
        , ntd.Night_OtherNonFree_Douyin AS ''晚间_其他非自由餐_抖音''
        , ntd.Night_OtherNonFree_RoomFee AS ''晚间_其他非自由餐_房费''
        , ntd.Night_OtherNonFree_Others AS ''晚间_其他非自由餐_其他''
        , ntd.Night_OtherNonFree_Subtotal AS ''晚间_其他非自由餐_小计''
        , ntd.Night_OtherNonFree_Revenue AS ''晚间_其他非自由餐_营业额''
    FROM
        OverviewAndDropInData AS ovd
    LEFT JOIN
        DayTimePivotedData AS dtp ON ovd.WorkDate = dtp.WorkDate
    LEFT JOIN
        NightTimeDetailData AS ntd ON ovd.WorkDate = ntd.WorkDate
    ORDER BY
        ovd.WorkDate;
    ';

    -- 使用 sp_executesql 执行动态 SQL，并传入参数
    EXEC sp_executesql @DynamicSQL,
        N'@BeginDate_Param date, @EndDate_Param date, @ShopId_Param int',
        @BeginDate_Param = @BeginDate,
        @EndDate_Param = @EndDate,
        @ShopId_Param = @ShopId;

END
GO
