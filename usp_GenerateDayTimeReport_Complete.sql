-- ====================================================================
-- KTV白天档报告存储过程 - 完整版本
-- 包含总览数据和完整的动态时段数据（含上一档直落）
-- 创建时间: 2025-01-23
-- ====================================================================

USE OperateData;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateDayTimeReport_Complete', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GenerateDayTimeReport_Complete;
GO

CREATE PROCEDURE dbo.usp_GenerateDayTimeReport_Complete
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL,
    @Debug bit = 0
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    -- 参数默认值处理
    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    -- 参数验证
    IF @ShopId <= 0
    BEGIN
        RAISERROR(N'门店ID必须大于0', 16, 1);
        RETURN;
    END

    BEGIN TRY
        -- 获取该门店的所有白天时段
        DECLARE @TimeSlots TABLE (
            TimeNo int,
            TimeName nvarchar(50),
            BegTime int,
            SortOrder int
        );

        INSERT INTO @TimeSlots
        SELECT ti.TimeNo, ti.TimeName, ti.BegTime, ROW_NUMBER() OVER (ORDER BY ti.BegTime)
        FROM dbo.shoptimeinfo sti
        JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
        WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000
        ORDER BY ti.BegTime;

        -- 构建动态列定义和选择列表
        DECLARE @ColumnDefs nvarchar(MAX) = '';
        DECLARE @SelectColumns nvarchar(MAX) = '';
        DECLARE @UpdateStatements nvarchar(MAX) = '';
        
        SELECT @ColumnDefs = @ColumnDefs + 
            ', [' + TimeName + '_K+] int DEFAULT 0' +
            ', [' + TimeName + '_特权预约] int DEFAULT 0' +
            ', [' + TimeName + '_美团] int DEFAULT 0' +
            ', [' + TimeName + '_抖音] int DEFAULT 0' +
            ', [' + TimeName + '_房费] int DEFAULT 0' +
            ', [' + TimeName + '_小计] int DEFAULT 0' +
            ', [' + TimeName + '_上一档直落] int DEFAULT 0'
        FROM @TimeSlots
        ORDER BY SortOrder;

        SELECT @SelectColumns = @SelectColumns + 
            ', [' + TimeName + '_K+]' +
            ', [' + TimeName + '_特权预约]' +
            ', [' + TimeName + '_美团]' +
            ', [' + TimeName + '_抖音]' +
            ', [' + TimeName + '_房费]' +
            ', [' + TimeName + '_小计]' +
            ', [' + TimeName + '_上一档直落]'
        FROM @TimeSlots
        ORDER BY SortOrder;

        -- 创建临时结果表
        DECLARE @CreateTableSQL nvarchar(MAX) = N'
        CREATE TABLE #FinalResults (
            日期 varchar(8),
            门店 nvarchar(100),
            星期 nvarchar(20),
            营收_总收入 decimal(18,2) DEFAULT 0,
            营收_白天档 decimal(18,2) DEFAULT 0,
            营收_晚上档 decimal(18,2) DEFAULT 0,
            带客_全天总批数 int DEFAULT 0,
            带客_白天档_总批次 int DEFAULT 0,
            带客_晚上档_总批次 int DEFAULT 0,
            带客_白天档_直落 int DEFAULT 0,
            带客_晚上档_直落 int DEFAULT 0,
            用餐_总人数 int DEFAULT 0,
            用餐_自助餐人数 int DEFAULT 0,
            用餐_直落人数 int DEFAULT 0' + @ColumnDefs + N'
        );';

        EXEC sp_executesql @CreateTableSQL;

        -- 循环处理每一天
        DECLARE @CurrentDate date = @BeginDate;
        
        WHILE @CurrentDate <= @EndDate
        BEGIN
            DECLARE @CurrentWorkDate varchar(8) = CONVERT(varchar(8), @CurrentDate, 112);
            
            -- 获取总览数据
            DECLARE @TotalRevenue decimal(18,2), @DayTimeRevenue decimal(18,2), @NightTimeRevenue decimal(18,2);
            DECLARE @TotalBatchCount int, @DayTimeBatchCount int, @NightTimeBatchCount int;
            DECLARE @DayTimeDropInBatch int, @NightTimeDropInBatch int;
            DECLARE @TotalGuestCount int, @BuffetGuestCount int, @TotalDropInGuests int;
            DECLARE @ShopName nvarchar(100), @WeekdayName nvarchar(20);

            SELECT 
                @TotalRevenue = SUM(rt.TotalAmount),
                @DayTimeRevenue = SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END),
                @NightTimeRevenue = SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END),
                @TotalBatchCount = COUNT(rt.InvNo),
                @DayTimeBatchCount = COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END),
                @NightTimeBatchCount = COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END),
                @TotalGuestCount = SUM(rt.Numbers),
                @BuffetGuestCount = SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END),
                @ShopName = MAX(b.ShopName),
                @WeekdayName = DATENAME(weekday, @CurrentDate),
                @DayTimeDropInBatch = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END),
                @NightTimeDropInBatch = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END),
                @TotalDropInGuests = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END)
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN MIMS.dbo.ShopInfo AS b ON rt.ShopId = b.Shopid
            LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
            LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
            LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
            WHERE rt.ShopId = @ShopId 
              AND rt.WorkDate = @CurrentWorkDate 
              AND rt.OpenDateTime IS NOT NULL;

            -- 插入基础数据行
            DECLARE @InsertSQL nvarchar(MAX) = N'
            INSERT INTO #FinalResults (日期, 门店, 星期, 营收_总收入, 营收_白天档, 营收_晚上档, 带客_全天总批数, 带客_白天档_总批次, 带客_晚上档_总批次, 带客_白天档_直落, 带客_晚上档_直落, 用餐_总人数, 用餐_自助餐人数, 用餐_直落人数)
            VALUES (
                ''' + @CurrentWorkDate + ''',
                N''' + ISNULL(@ShopName, '') + ''',
                N''' + ISNULL(@WeekdayName, '') + ''',
                ' + CAST(ISNULL(@TotalRevenue, 0) AS varchar(20)) + ',
                ' + CAST(ISNULL(@DayTimeRevenue, 0) AS varchar(20)) + ',
                ' + CAST(ISNULL(@NightTimeRevenue, 0) AS varchar(20)) + ',
                ' + CAST(ISNULL(@TotalBatchCount, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@DayTimeBatchCount, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@NightTimeBatchCount, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@DayTimeDropInBatch, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@NightTimeDropInBatch, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@TotalGuestCount, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@BuffetGuestCount, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@TotalDropInGuests, 0) AS varchar(10)) + '
            );';

            EXEC sp_executesql @InsertSQL;

            -- 更新时段数据
            DECLARE @TimeSlotSQL nvarchar(MAX) = N'
            WITH TimeSlots AS (
                SELECT
                    ti.TimeNo, ti.TimeName, ti.BegTime,
                    DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(''' + CONVERT(varchar(10), @CurrentDate, 120) + ''' AS datetime))) AS SlotStartDateTime,
                    LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(''' + CONVERT(varchar(10), @CurrentDate, 120) + ''' AS datetime))), 1, ''2999-12-31'') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
                FROM dbo.shoptimeinfo AS sti
                JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo
                WHERE sti.ShopId = ' + CAST(@ShopId AS varchar(10)) + '
            ),
            TrueDropInData AS (
                SELECT rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key
                FROM dbo.RmCloseInfo_Test AS rt
                JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
                JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
                WHERE rt.ShopId = ' + CAST(@ShopId AS varchar(10)) + ' 
                  AND rt.WorkDate = ''' + @CurrentWorkDate + ''' 
                  AND rt.OpenDateTime IS NOT NULL
                  AND rt.Beg_Key <> rt.End_Key
                  AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
                  AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
            ),
            TimeSlotStats AS (
                SELECT 
                    ti_main.TimeName,
                    COUNT(CASE WHEN rt.CtNo = 2 THEN 1 ELSE NULL END) AS KPlus,
                    COUNT(CASE WHEN rt.AliPay > 0 THEN 1 ELSE NULL END) AS Special,
                    COUNT(CASE WHEN rt.MTPay > 0 THEN 1 ELSE NULL END) AS Meituan,
                    COUNT(CASE WHEN rt.DZPay > 0 THEN 1 ELSE NULL END) AS Douyin,
                    COUNT(CASE WHEN rt.CtNo = 1 THEN 1 ELSE NULL END) AS RoomFee,
                    COUNT(rt.InvNo) AS Subtotal,
                    ISNULL((
                        SELECT COUNT(tdi.InvNo)
                        FROM TrueDropInData AS tdi
                        JOIN TimeSlots ts_beg ON tdi.Beg_Key = ts_beg.TimeNo
                        WHERE ts_beg.BegTime < ti_main.BegTime
                          AND tdi.CloseDatetime > ts_main.NextSlotStartDateTime
                    ), 0) AS DropInFromPrevious
                FROM dbo.RmCloseInfo_Test AS rt
                JOIN dbo.timeinfo AS ti_main ON rt.Beg_Key = ti_main.TimeNo
                JOIN TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo
                WHERE rt.ShopId = ' + CAST(@ShopId AS varchar(10)) + '
                  AND rt.WorkDate = ''' + @CurrentWorkDate + '''
                  AND rt.OpenDateTime IS NOT NULL
                  AND ti_main.BegTime < 2000
                GROUP BY rt.Beg_Key, ti_main.TimeName, ti_main.BegTime, ts_main.NextSlotStartDateTime
            )
            SELECT TimeName, KPlus, Special, Meituan, Douyin, RoomFee, Subtotal, DropInFromPrevious
            FROM TimeSlotStats
            ORDER BY (SELECT BegTime FROM TimeSlots WHERE TimeName = TimeSlotStats.TimeName)';

            -- 创建临时表存储时段数据
            CREATE TABLE #TimeSlotData (
                TimeName nvarchar(50),
                KPlus int,
                Special int,
                Meituan int,
                Douyin int,
                RoomFee int,
                Subtotal int,
                DropInFromPrevious int
            );

            INSERT INTO #TimeSlotData
            EXEC sp_executesql @TimeSlotSQL;

            -- 为每个时段更新对应的列
            DECLARE @TimeName nvarchar(50), @KPlus int, @Special int, @Meituan int, @Douyin int, @RoomFee int, @Subtotal int, @DropIn int;
            
            DECLARE time_cursor CURSOR FOR
            SELECT TimeName, KPlus, Special, Meituan, Douyin, RoomFee, Subtotal, DropInFromPrevious
            FROM #TimeSlotData;

            OPEN time_cursor;
            FETCH NEXT FROM time_cursor INTO @TimeName, @KPlus, @Special, @Meituan, @Douyin, @RoomFee, @Subtotal, @DropIn;

            WHILE @@FETCH_STATUS = 0
            BEGIN
                DECLARE @UpdateSQL nvarchar(MAX) = N'
                UPDATE #FinalResults 
                SET [' + @TimeName + '_K+] = ' + CAST(@KPlus AS varchar(10)) + ',
                    [' + @TimeName + '_特权预约] = ' + CAST(@Special AS varchar(10)) + ',
                    [' + @TimeName + '_美团] = ' + CAST(@Meituan AS varchar(10)) + ',
                    [' + @TimeName + '_抖音] = ' + CAST(@Douyin AS varchar(10)) + ',
                    [' + @TimeName + '_房费] = ' + CAST(@RoomFee AS varchar(10)) + ',
                    [' + @TimeName + '_小计] = ' + CAST(@Subtotal AS varchar(10)) + ',
                    [' + @TimeName + '_上一档直落] = ' + CAST(@DropIn AS varchar(10)) + '
                WHERE 日期 = ''' + @CurrentWorkDate + '''';

                EXEC sp_executesql @UpdateSQL;

                FETCH NEXT FROM time_cursor INTO @TimeName, @KPlus, @Special, @Meituan, @Douyin, @RoomFee, @Subtotal, @DropIn;
            END

            CLOSE time_cursor;
            DEALLOCATE time_cursor;

            DROP TABLE #TimeSlotData;

            SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
        END

        -- 返回结果
        DECLARE @FinalSelectSQL nvarchar(MAX) = N'SELECT 日期, 门店, 星期, 营收_总收入, 营收_白天档, 营收_晚上档, 带客_全天总批数, 带客_白天档_总批次, 带客_晚上档_总批次, 带客_白天档_直落, 带客_晚上档_直落, 用餐_总人数, 用餐_自助餐人数, 用餐_直落人数' + @SelectColumns + N' FROM #FinalResults ORDER BY 日期';
        
        EXEC sp_executesql @FinalSelectSQL;

        -- 清理临时表
        DROP TABLE #FinalResults;

        -- 调试信息输出
        IF @Debug = 1
        BEGIN
            PRINT N'存储过程执行完成 - 白天档完整版本';
            PRINT N'查询日期范围: ' + CONVERT(nvarchar(10), @BeginDate, 120) + N' 到 ' + CONVERT(nvarchar(10), @EndDate, 120);
            PRINT N'门店ID: ' + CAST(@ShopId AS nvarchar(10));
        END

    END TRY
    BEGIN CATCH
        -- 清理临时表
        IF OBJECT_ID('tempdb..#FinalResults') IS NOT NULL
            DROP TABLE #FinalResults;
        IF OBJECT_ID('tempdb..#TimeSlotData') IS NOT NULL
            DROP TABLE #TimeSlotData;
            
        DECLARE @ErrorMessage nvarchar(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity int = ERROR_SEVERITY();
        DECLARE @ErrorState int = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO
