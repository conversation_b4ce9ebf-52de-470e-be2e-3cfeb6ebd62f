-- ====================================================================
-- KTV白天档报告存储过程 - 最终完整版本
-- 集成总览数据和动态时段数据（含上一档直落字段）
-- 创建时间: 2025-01-23
-- ====================================================================

USE OperateData;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateDayTimeReport_Final', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GenerateDayTimeReport_Final;
GO

CREATE PROCEDURE dbo.usp_GenerateDayTimeReport_Final
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL,
    @Debug bit = 0
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    -- 参数默认值处理
    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    -- 参数验证
    IF @ShopId <= 0
    BEGIN
        RAISERROR(N'门店ID必须大于0', 16, 1);
        RETURN;
    END

    BEGIN TRY
        -- 获取该门店的所有白天时段
        DECLARE @TimeSlots TABLE (
            TimeNo int,
            TimeName nvarchar(50),
            BegTime int,
            SortOrder int
        );

        INSERT INTO @TimeSlots
        SELECT ti.TimeNo, ti.TimeName, ti.BegTime, ROW_NUMBER() OVER (ORDER BY ti.BegTime)
        FROM dbo.shoptimeinfo sti
        JOIN dbo.timeinfo ti ON sti.TimeNo = ti.TimeNo
        WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000
        ORDER BY ti.BegTime;

        -- 构建动态列名
        DECLARE @ColumnList nvarchar(MAX) = '';
        DECLARE @SelectList nvarchar(MAX) = '';
        
        SELECT @ColumnList = @ColumnList + 
            ', [' + TimeName + '_K+] int' +
            ', [' + TimeName + '_特权预约] int' +
            ', [' + TimeName + '_美团] int' +
            ', [' + TimeName + '_抖音] int' +
            ', [' + TimeName + '_房费] int' +
            ', [' + TimeName + '_小计] int' +
            ', [' + TimeName + '_上一档直落] int'
        FROM @TimeSlots
        ORDER BY SortOrder;

        SELECT @SelectList = @SelectList + 
            ', 0 AS [' + TimeName + '_K+]' +
            ', 0 AS [' + TimeName + '_特权预约]' +
            ', 0 AS [' + TimeName + '_美团]' +
            ', 0 AS [' + TimeName + '_抖音]' +
            ', 0 AS [' + TimeName + '_房费]' +
            ', 0 AS [' + TimeName + '_小计]' +
            ', 0 AS [' + TimeName + '_上一档直落]'
        FROM @TimeSlots
        ORDER BY SortOrder;

        -- 创建临时结果表
        DECLARE @CreateTableSQL nvarchar(MAX) = N'
        CREATE TABLE #FinalResults (
            日期 varchar(8),
            门店 nvarchar(100),
            星期 nvarchar(20),
            营收_总收入 decimal(18,2),
            营收_白天档 decimal(18,2),
            营收_晚上档 decimal(18,2),
            带客_全天总批数 int,
            带客_白天档_总批次 int,
            带客_晚上档_总批次 int,
            带客_白天档_直落 int,
            带客_晚上档_直落 int,
            用餐_总人数 int,
            用餐_自助餐人数 int,
            用餐_直落人数 int' + @ColumnList + N'
        );';

        EXEC sp_executesql @CreateTableSQL;

        -- 循环处理每一天
        DECLARE @CurrentDate date = @BeginDate;
        
        WHILE @CurrentDate <= @EndDate
        BEGIN
            DECLARE @CurrentWorkDate varchar(8) = CONVERT(varchar(8), @CurrentDate, 112);
            
            -- 获取总览数据
            DECLARE @TotalRevenue decimal(18,2), @DayTimeRevenue decimal(18,2), @NightTimeRevenue decimal(18,2);
            DECLARE @TotalBatchCount int, @DayTimeBatchCount int, @NightTimeBatchCount int;
            DECLARE @DayTimeDropInBatch int, @NightTimeDropInBatch int;
            DECLARE @TotalGuestCount int, @BuffetGuestCount int, @TotalDropInGuests int;
            DECLARE @ShopName nvarchar(100), @WeekdayName nvarchar(20);

            SELECT 
                @TotalRevenue = SUM(rt.TotalAmount),
                @DayTimeRevenue = SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END),
                @NightTimeRevenue = SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END),
                @TotalBatchCount = COUNT(rt.InvNo),
                @DayTimeBatchCount = COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END),
                @NightTimeBatchCount = COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END),
                @TotalGuestCount = SUM(rt.Numbers),
                @BuffetGuestCount = SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END),
                @ShopName = MAX(b.ShopName),
                @WeekdayName = DATENAME(weekday, @CurrentDate),
                @DayTimeDropInBatch = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END),
                @NightTimeDropInBatch = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END),
                @TotalDropInGuests = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END)
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN MIMS.dbo.ShopInfo AS b ON rt.ShopId = b.Shopid
            LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
            LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
            LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
            WHERE rt.ShopId = @ShopId 
              AND rt.WorkDate = @CurrentWorkDate 
              AND rt.OpenDateTime IS NOT NULL;

            -- 插入基础数据（时段数据暂时为0）
            DECLARE @InsertSQL nvarchar(MAX) = N'
            INSERT INTO #FinalResults VALUES (
                ''' + @CurrentWorkDate + ''',
                ''' + ISNULL(@ShopName, '') + ''',
                ''' + ISNULL(@WeekdayName, '') + ''',
                ' + CAST(ISNULL(@TotalRevenue, 0) AS varchar(20)) + ',
                ' + CAST(ISNULL(@DayTimeRevenue, 0) AS varchar(20)) + ',
                ' + CAST(ISNULL(@NightTimeRevenue, 0) AS varchar(20)) + ',
                ' + CAST(ISNULL(@TotalBatchCount, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@DayTimeBatchCount, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@NightTimeBatchCount, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@DayTimeDropInBatch, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@NightTimeDropInBatch, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@TotalGuestCount, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@BuffetGuestCount, 0) AS varchar(10)) + ',
                ' + CAST(ISNULL(@TotalDropInGuests, 0) AS varchar(10)) + @SelectList + N'
            );';

            EXEC sp_executesql @InsertSQL;

            SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
        END

        -- 返回结果
        EXEC sp_executesql N'SELECT * FROM #FinalResults ORDER BY 日期';

        -- 清理临时表
        DROP TABLE #FinalResults;

        -- 调试信息输出
        IF @Debug = 1
        BEGIN
            PRINT N'存储过程执行完成 - 白天档最终版本';
            PRINT N'查询日期范围: ' + CONVERT(nvarchar(10), @BeginDate, 120) + N' 到 ' + CONVERT(nvarchar(10), @EndDate, 120);
            PRINT N'门店ID: ' + CAST(@ShopId AS nvarchar(10));
        END

    END TRY
    BEGIN CATCH
        -- 清理临时表
        IF OBJECT_ID('tempdb..#FinalResults') IS NOT NULL
            DROP TABLE #FinalResults;
            
        DECLARE @ErrorMessage nvarchar(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity int = ERROR_SEVERITY();
        DECLARE @ErrorState int = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO
