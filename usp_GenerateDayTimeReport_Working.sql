-- ====================================================================
-- KTV白天档报告存储过程 - 工作版本
-- 包含总览数据和时段数据，使用固定列结构
-- 创建时间: 2025-01-23
-- ====================================================================

USE OperateData;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateDayTimeReport_Working', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GenerateDayTimeReport_Working;
GO

CREATE PROCEDURE dbo.usp_GenerateDayTimeReport_Working
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL,
    @Debug bit = 0
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    -- 参数默认值处理
    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    -- 参数验证
    IF @ShopId <= 0
    BEGIN
        RAISERROR(N'门店ID必须大于0', 16, 1);
        RETURN;
    END

    BEGIN TRY
        -- 创建结果表，包含基础字段和时段字段
        CREATE TABLE #Results (
            日期 varchar(8),
            门店 nvarchar(100),
            星期 nvarchar(20),
            营收_总收入 decimal(18,2),
            营收_白天档 decimal(18,2),
            营收_晚上档 decimal(18,2),
            带客_全天总批数 int,
            带客_白天档_总批次 int,
            带客_晚上档_总批次 int,
            带客_白天档_直落 int,
            带客_晚上档_直落 int,
            用餐_总人数 int,
            用餐_自助餐人数 int,
            用餐_直落人数 int,
            时段数据 nvarchar(MAX)
        );

        -- 循环处理每一天
        DECLARE @CurrentDate date = @BeginDate;
        
        WHILE @CurrentDate <= @EndDate
        BEGIN
            DECLARE @CurrentWorkDate varchar(8) = CONVERT(varchar(8), @CurrentDate, 112);
            
            -- 获取总览数据
            DECLARE @TotalRevenue decimal(18,2), @DayTimeRevenue decimal(18,2), @NightTimeRevenue decimal(18,2);
            DECLARE @TotalBatchCount int, @DayTimeBatchCount int, @NightTimeBatchCount int;
            DECLARE @DayTimeDropInBatch int, @NightTimeDropInBatch int;
            DECLARE @TotalGuestCount int, @BuffetGuestCount int, @TotalDropInGuests int;
            DECLARE @ShopName nvarchar(100), @WeekdayName nvarchar(20);

            SELECT 
                @TotalRevenue = SUM(rt.TotalAmount),
                @DayTimeRevenue = SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN rt.TotalAmount ELSE 0 END),
                @NightTimeRevenue = SUM(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN rt.TotalAmount ELSE 0 END),
                @TotalBatchCount = COUNT(rt.InvNo),
                @DayTimeBatchCount = COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) < 20 THEN 1 ELSE NULL END),
                @NightTimeBatchCount = COUNT(CASE WHEN DATEPART(hour, rt.OpenDateTime) >= 20 THEN 1 ELSE NULL END),
                @TotalGuestCount = SUM(rt.Numbers),
                @BuffetGuestCount = SUM(rt.Numbers) - SUM(CASE WHEN rt.CtNo = 1 THEN rt.Numbers ELSE 0 END),
                @ShopName = MAX(b.ShopName),
                @WeekdayName = DATENAME(weekday, @CurrentDate),
                @DayTimeDropInBatch = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime < 1700 THEN 1 ELSE 0 END),
                @NightTimeDropInBatch = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 AND ti_beg.BegTime >= 1700 AND ti_beg.BegTime < 2000 THEN 1 ELSE 0 END),
                @TotalDropInGuests = SUM(CASE WHEN rt.Beg_Key <> rt.End_Key AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1 AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180 THEN rt.Numbers ELSE 0 END)
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN MIMS.dbo.ShopInfo AS b ON rt.ShopId = b.Shopid
            LEFT JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
            LEFT JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
            LEFT JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
            WHERE rt.ShopId = @ShopId 
              AND rt.WorkDate = @CurrentWorkDate 
              AND rt.OpenDateTime IS NOT NULL;

            -- 获取时段数据
            DECLARE @TimeSlotData nvarchar(MAX) = '';
            
            -- 创建临时表存储时段数据
            CREATE TABLE #TempTimeSlots (
                TimeName nvarchar(50),
                KPlus int,
                Special int,
                Meituan int,
                Douyin int,
                RoomFee int,
                Subtotal int,
                DropInFromPrevious int
            );

            -- 使用您提供的逻辑获取时段数据
            INSERT INTO #TempTimeSlots
            EXEC sp_executesql N'
            WITH TimeSlots AS (
                SELECT
                    ti.TimeNo, ti.TimeName, ti.BegTime,
                    DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@TargetDate AS datetime))) AS SlotStartDateTime,
                    LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), CAST(@TargetDate AS datetime))), 1, ''2999-12-31'') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
                FROM dbo.shoptimeinfo AS sti
                JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo
                WHERE sti.ShopId = @ShopIdParam
            ),
            TrueDropInData AS (
                SELECT rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key
                FROM dbo.RmCloseInfo_Test AS rt
                JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
                JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
                WHERE rt.ShopId = @ShopIdParam 
                  AND rt.WorkDate = @WorkDateParam 
                  AND rt.OpenDateTime IS NOT NULL
                  AND rt.Beg_Key <> rt.End_Key
                  AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
                  AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
            )
            SELECT 
                ti_main.TimeName,
                COUNT(CASE WHEN rt.CtNo = 2 THEN 1 ELSE NULL END) AS KPlus,
                COUNT(CASE WHEN rt.AliPay > 0 THEN 1 ELSE NULL END) AS Special,
                COUNT(CASE WHEN rt.MTPay > 0 THEN 1 ELSE NULL END) AS Meituan,
                COUNT(CASE WHEN rt.DZPay > 0 THEN 1 ELSE NULL END) AS Douyin,
                COUNT(CASE WHEN rt.CtNo = 1 THEN 1 ELSE NULL END) AS RoomFee,
                COUNT(rt.InvNo) AS Subtotal,
                ISNULL((
                    SELECT COUNT(tdi.InvNo)
                    FROM TrueDropInData AS tdi
                    JOIN TimeSlots ts_beg ON tdi.Beg_Key = ts_beg.TimeNo
                    WHERE ts_beg.BegTime < ti_main.BegTime
                      AND tdi.CloseDatetime > ts_main.NextSlotStartDateTime
                ), 0) AS DropInFromPrevious
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN dbo.timeinfo AS ti_main ON rt.Beg_Key = ti_main.TimeNo
            JOIN TimeSlots AS ts_main ON rt.Beg_Key = ts_main.TimeNo
            WHERE rt.ShopId = @ShopIdParam
              AND rt.WorkDate = @WorkDateParam
              AND rt.OpenDateTime IS NOT NULL
              AND ti_main.BegTime < 2000
            GROUP BY rt.Beg_Key, ti_main.TimeName, ti_main.BegTime, ts_main.NextSlotStartDateTime
            ORDER BY ti_main.BegTime',
            N'@ShopIdParam int, @WorkDateParam varchar(8), @TargetDate date',
            @ShopIdParam = @ShopId,
            @WorkDateParam = @CurrentWorkDate,
            @TargetDate = @CurrentDate;

            -- 构建时段数据字符串
            SELECT @TimeSlotData = @TimeSlotData + 
                TimeName + ':K+=' + CAST(KPlus AS varchar(10)) + 
                ',特权=' + CAST(Special AS varchar(10)) + 
                ',美团=' + CAST(Meituan AS varchar(10)) + 
                ',抖音=' + CAST(Douyin AS varchar(10)) + 
                ',房费=' + CAST(RoomFee AS varchar(10)) + 
                ',小计=' + CAST(Subtotal AS varchar(10)) + 
                ',上一档直落=' + CAST(DropInFromPrevious AS varchar(10)) + '; '
            FROM #TempTimeSlots
            ORDER BY (SELECT BegTime FROM dbo.timeinfo ti JOIN dbo.shoptimeinfo sti ON ti.TimeNo = sti.TimeNo WHERE sti.ShopId = @ShopId AND ti.TimeName = #TempTimeSlots.TimeName);

            DROP TABLE #TempTimeSlots;

            -- 插入当天数据
            INSERT INTO #Results VALUES (
                @CurrentWorkDate, 
                ISNULL(@ShopName, ''), 
                ISNULL(@WeekdayName, ''),
                ISNULL(@TotalRevenue, 0), 
                ISNULL(@DayTimeRevenue, 0), 
                ISNULL(@NightTimeRevenue, 0),
                ISNULL(@TotalBatchCount, 0), 
                ISNULL(@DayTimeBatchCount, 0), 
                ISNULL(@NightTimeBatchCount, 0),
                ISNULL(@DayTimeDropInBatch, 0), 
                ISNULL(@NightTimeDropInBatch, 0),
                ISNULL(@TotalGuestCount, 0), 
                ISNULL(@BuffetGuestCount, 0), 
                ISNULL(@TotalDropInGuests, 0),
                @TimeSlotData
            );

            SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
        END

        -- 返回结果
        SELECT * FROM #Results ORDER BY 日期;

        -- 清理临时表
        DROP TABLE #Results;

        -- 调试信息输出
        IF @Debug = 1
        BEGIN
            PRINT N'存储过程执行完成 - 白天档工作版本';
            PRINT N'查询日期范围: ' + CONVERT(nvarchar(10), @BeginDate, 120) + N' 到 ' + CONVERT(nvarchar(10), @EndDate, 120);
            PRINT N'门店ID: ' + CAST(@ShopId AS nvarchar(10));
        END

    END TRY
    BEGIN CATCH
        -- 清理临时表
        IF OBJECT_ID('tempdb..#Results') IS NOT NULL
            DROP TABLE #Results;
        IF OBJECT_ID('tempdb..#TempTimeSlots') IS NOT NULL
            DROP TABLE #TempTimeSlots;
            
        DECLARE @ErrorMessage nvarchar(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity int = ERROR_SEVERITY();
        DECLARE @ErrorState int = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO
