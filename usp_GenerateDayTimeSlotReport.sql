-- 连接到 OperateData 数据库
USE OperateData;
GO

-- 设置必要的选项
SET QUOTED_IDENTIFIER ON;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateDayTimeSlotReport', 'P') IS NOT NULL
DROP PROCEDURE dbo.usp_GenerateDayTimeSlotReport;
GO

CREATE PROCEDURE dbo.usp_GenerateDayTimeSlotReport
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    -- ====================================================================
    -- 步骤 1: 参数处理
    -- ====================================================================
    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    -- ====================================================================
    -- 步骤 2: 创建临时表存储结果
    -- ====================================================================
    CREATE TABLE #TimeSlotResults (
        WorkDate varchar(8),
        TimeName nvarchar(50),
        BegTime int,
        KPlus int,
        Special int,
        Meituan int,
        Douyin int,
        RoomFee int,
        Subtotal int,
        PreviousDropIn int  -- 上一档直落
    );

    -- ====================================================================
    -- 步骤 3: 循环处理每个日期
    -- ====================================================================
    DECLARE @CurrentDate date = @BeginDate;
    DECLARE @CurrentWorkDate varchar(8);

    WHILE @CurrentDate <= @EndDate
    BEGIN
        SET @CurrentWorkDate = FORMAT(@CurrentDate, 'yyyyMMdd');
        
        -- 使用优化的CTE方式处理单日分时段数据
        WITH 
        -- CTE 1: 准备当天所有生效的业务时间档，并获取每个档的"下一个档"的开始时间
        TimeSlots AS (
            SELECT
                ti.TimeNo, ti.TimeName, ti.BegTime,
                DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), @CurrentDate)) AS SlotStartDateTime,
                -- 使用 LEAD() 窗口函数获取下一个时间档的开始时间
                LEAD(DATEADD(minute, (ti.BegTime % 100), DATEADD(hour, (ti.BegTime / 100), @CurrentDate)), 1, '2999-12-31') OVER (ORDER BY ti.BegTime) AS NextSlotStartDateTime
            FROM dbo.shoptimeinfo AS sti
            JOIN dbo.timeinfo AS ti ON sti.TimeNo = ti.TimeNo
            WHERE sti.ShopId = @ShopId
                AND ti.BegTime < 2000  -- 只处理白天档
        ),
        -- CTE 2: 筛选出当天所有的"真直落"消费记录
        TrueDropInData AS (
            SELECT 
                rt.InvNo, rt.OpenDateTime, rt.CloseDatetime, rt.Beg_Key, rt.Numbers,
                ti_beg.BegTime AS BegSlotTime
            FROM dbo.RmCloseInfo_Test AS rt
            JOIN dbo.shoptimeinfo AS sti_beg ON rt.ShopId = sti_beg.ShopId AND rt.Beg_Key = sti_beg.TimeNo
            JOIN dbo.timeinfo AS ti_beg ON sti_beg.TimeNo = ti_beg.TimeNo
            JOIN dbo.shoptimeinfo AS sti_end ON rt.ShopId = sti_end.ShopId AND rt.End_Key = sti_end.TimeNo
            WHERE rt.ShopId = @ShopId 
                AND rt.WorkDate = @CurrentWorkDate 
                AND rt.OpenDateTime IS NOT NULL
                AND rt.Beg_Key <> rt.End_Key
                AND dbo.fn_IsTimeTypeOverlap(sti_beg.TimeType, sti_end.TimeType) = 1
                AND DATEDIFF(minute, rt.OpenDateTime, rt.CloseDatetime) >= 180
        ),
        -- CTE 3: 当天的基础订单数据
        BaseOrders AS (
            SELECT 
                rt.InvNo, rt.Beg_Key, rt.CtNo, rt.MTPay, rt.DZPay, rt.AliPay
            FROM dbo.RmCloseInfo_Test AS rt
            WHERE rt.ShopId = @ShopId 
                AND rt.WorkDate = @CurrentWorkDate 
                AND rt.OpenDateTime IS NOT NULL
        )
        -- 插入当日分时段统计结果
        INSERT INTO #TimeSlotResults (
            WorkDate, TimeName, BegTime, KPlus, Special, Meituan, Douyin, RoomFee, Subtotal, PreviousDropIn
        )
        SELECT
            @CurrentWorkDate,
            ts.TimeName,
            ts.BegTime,
            -- 各渠道统计
            COUNT(CASE WHEN bo.CtNo = 2 THEN 1 ELSE NULL END) AS KPlus,
            COUNT(CASE WHEN bo.AliPay > 0 THEN 1 ELSE NULL END) AS Special,
            COUNT(CASE WHEN bo.MTPay > 0 THEN 1 ELSE NULL END) AS Meituan,
            COUNT(CASE WHEN bo.DZPay > 0 THEN 1 ELSE NULL END) AS Douyin,
            COUNT(CASE WHEN bo.CtNo = 1 THEN 1 ELSE NULL END) AS RoomFee,
            COUNT(bo.InvNo) AS Subtotal,
            -- 上一档直落批次 (优化版本)
            ISNULL(
                (
                    SELECT COUNT(tdi.InvNo)
                    FROM TrueDropInData AS tdi
                    WHERE
                        -- 条件1: 直落单的 Beg_Key 必须早于当前主查询的时间档
                        tdi.BegSlotTime < ts.BegTime
                        -- 条件2: 并且，直落单的结账时间必须晚于当前主查询时间档的【下一个档】的开始时间
                        AND tdi.CloseDatetime > ts.NextSlotStartDateTime
                ), 0
            ) AS PreviousDropIn
        FROM TimeSlots AS ts
        LEFT JOIN BaseOrders AS bo ON ts.TimeNo = bo.Beg_Key
        GROUP BY ts.TimeNo, ts.TimeName, ts.BegTime, ts.NextSlotStartDateTime
        ORDER BY ts.BegTime;

        SET @CurrentDate = DATEADD(day, 1, @CurrentDate);
    END

    -- ====================================================================
    -- 步骤 4: 输出最终结果
    -- ====================================================================
    SELECT 
        WorkDate AS '日期',
        TimeName AS '时间段',
        KPlus AS 'K+',
        Special AS '特权预约',
        Meituan AS '美团',
        Douyin AS '抖音',
        RoomFee AS '房费',
        Subtotal AS '小计',
        PreviousDropIn AS '上一档直落'
    FROM #TimeSlotResults
    ORDER BY WorkDate, BegTime;

    -- 清理临时表
    DROP TABLE #TimeSlotResults;

END
GO
