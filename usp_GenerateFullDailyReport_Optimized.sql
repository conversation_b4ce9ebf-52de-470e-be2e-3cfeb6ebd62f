-- ====================================================================
-- KTV每日报告存储过程 - 全面优化版本
-- 解决字符编码、性能和数据准确性问题
-- 创建时间: 2025-01-23
-- 优化内容: 
-- 1. 修复字符编码问题 (UTF-8/Unicode处理)
-- 2. 性能优化 (减少子查询，优化JOIN)
-- 3. 数据分类逻辑修复 (买断/畅饮/自由消套餐)
-- 4. 添加错误处理和性能监控
-- ====================================================================

USE OperateData;
GO

-- 设置必要的选项和编码
SET NOCOUNT ON;
SET QUOTED_IDENTIFIER ON;
SET ANSI_NULLS ON;
-- 确保正确的字符编码处理
SET ANSI_PADDING ON;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateFullDailyReport_Optimized', 'P') IS NOT NULL
DROP PROCEDURE dbo.usp_GenerateFullDailyReport_Optimized;
GO

CREATE PROCEDURE dbo.usp_GenerateFullDailyReport_Optimized
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL,
    @Debug bit = 0  -- 调试模式开关
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;
    
    -- 性能监控变量
    DECLARE @StartTime datetime2 = GETDATE();
    DECLARE @StepTime datetime2;
    
    -- 错误处理
    BEGIN TRY
        -- ====================================================================
        -- 步骤 1: 参数处理和验证
        -- ====================================================================
        IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
        IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());
        
        -- 参数验证
        IF @ShopId <= 0
        BEGIN
            RAISERROR(N'门店ID必须大于0', 16, 1);
            RETURN;
        END
        
        IF @BeginDate > @EndDate
        BEGIN
            RAISERROR(N'开始日期不能大于结束日期', 16, 1);
            RETURN;
        END
        
        IF @Debug = 1
        BEGIN
            PRINT N'参数验证完成 - 门店ID: ' + CAST(@ShopId AS nvarchar(10)) + 
                  N', 开始日期: ' + CAST(@BeginDate AS nvarchar(10)) + 
                  N', 结束日期: ' + CAST(@EndDate AS nvarchar(10));
        END

        -- ====================================================================
        -- 步骤 2: 预处理分类数据 (优化性能)
        -- ====================================================================
        SET @StepTime = GETDATE();
        
        -- 创建临时表存储分类信息，避免重复子查询
        IF OBJECT_ID('tempdb..#OrderClassifications') IS NOT NULL DROP TABLE #OrderClassifications;
        
        CREATE TABLE #OrderClassifications (
            InvNo nvarchar(50) COLLATE DATABASE_DEFAULT NOT NULL,
            HasBuyout bit NOT NULL DEFAULT 0,
            HasChangyin bit NOT NULL DEFAULT 0,
            HasFreeConsume bit NOT NULL DEFAULT 0,
            PRIMARY KEY (InvNo)
        );
        
        -- 预处理买断分类 (优先级最高)
        INSERT INTO #OrderClassifications (InvNo, HasBuyout)
        SELECT DISTINCT 
            fcb.InvNo COLLATE DATABASE_DEFAULT,
            1
        FROM dbo.FdCashBak fcb WITH(NOLOCK)
        WHERE fcb.FdCName LIKE N'%买断%'
            AND EXISTS (
                SELECT 1 FROM dbo.RmCloseInfo_Test rt WITH(NOLOCK)
                WHERE rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
                    AND rt.ShopId = @ShopId
                    AND CAST(rt.WorkDate AS date) >= @BeginDate 
                    AND CAST(rt.WorkDate AS date) <= @EndDate
            );
        
        -- 预处理畅饮分类 (次优先级) - 修复SQL Server语法
        MERGE #OrderClassifications AS target
        USING (
            SELECT DISTINCT fcb.InvNo COLLATE DATABASE_DEFAULT AS InvNo
            FROM dbo.FdCashBak fcb WITH(NOLOCK)
            WHERE fcb.FdCName LIKE N'%畅饮%'
                AND NOT EXISTS (SELECT 1 FROM #OrderClassifications oc WHERE oc.InvNo = fcb.InvNo COLLATE DATABASE_DEFAULT AND oc.HasBuyout = 1)
                AND EXISTS (
                    SELECT 1 FROM dbo.RmCloseInfo_Test rt WITH(NOLOCK)
                    WHERE rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
                        AND rt.ShopId = @ShopId
                        AND CAST(rt.WorkDate AS date) >= @BeginDate
                        AND CAST(rt.WorkDate AS date) <= @EndDate
                )
        ) AS source ON target.InvNo = source.InvNo
        WHEN MATCHED THEN UPDATE SET HasChangyin = 1
        WHEN NOT MATCHED THEN INSERT (InvNo, HasChangyin) VALUES (source.InvNo, 1);

        -- 预处理自由消套餐分类 (第三优先级) - 修复SQL Server语法
        MERGE #OrderClassifications AS target
        USING (
            SELECT DISTINCT fcb.InvNo COLLATE DATABASE_DEFAULT AS InvNo
            FROM dbo.FdCashBak fcb WITH(NOLOCK)
            WHERE fcb.FdCName LIKE N'%自由消%'
                AND NOT EXISTS (SELECT 1 FROM #OrderClassifications oc WHERE oc.InvNo = fcb.InvNo COLLATE DATABASE_DEFAULT AND (oc.HasBuyout = 1 OR oc.HasChangyin = 1))
                AND EXISTS (
                    SELECT 1 FROM dbo.RmCloseInfo_Test rt WITH(NOLOCK)
                    WHERE rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
                        AND rt.ShopId = @ShopId
                        AND CAST(rt.WorkDate AS date) >= @BeginDate
                        AND CAST(rt.WorkDate AS date) <= @EndDate
                )
        ) AS source ON target.InvNo = source.InvNo
        WHEN MATCHED THEN UPDATE SET HasFreeConsume = 1
        WHEN NOT MATCHED THEN INSERT (InvNo, HasFreeConsume) VALUES (source.InvNo, 1);

        IF @Debug = 1
        BEGIN
            PRINT N'分类预处理完成 - 耗时: ' + CAST(DATEDIFF(ms, @StepTime, GETDATE()) AS nvarchar(10)) + N'ms';
            SET @StepTime = GETDATE();
        END

        -- ====================================================================
        -- 步骤 3: 动态构建白天分时段统计的列
        -- ====================================================================
        DECLARE @PivotColumns nvarchar(MAX) = N'';
        DECLARE @PivotSelectColumns nvarchar(MAX) = N'';

        -- 优化：预先获取时间段信息，减少重复查询
        IF OBJECT_ID('tempdb..#TimeSlots') IS NOT NULL DROP TABLE #TimeSlots;
        
        SELECT DISTINCT 
            ti.TimeName, 
            ti.BegTime,
            -- 使用NCHAR确保Unicode字符正确处理
            QUOTENAME(ti.TimeName + N'_K+') AS KPlusCol,
            QUOTENAME(ti.TimeName + N'_特权预约') AS SpecialCol,
            QUOTENAME(ti.TimeName + N'_美团') AS MeituanCol,
            QUOTENAME(ti.TimeName + N'_抖音') AS DouyinCol,
            QUOTENAME(ti.TimeName + N'_房费') AS RoomFeeCol,
            QUOTENAME(ti.TimeName + N'_小计') AS SubtotalCol
        INTO #TimeSlots
        FROM dbo.shoptimeinfo sti WITH(NOLOCK)
        JOIN dbo.timeinfo ti WITH(NOLOCK) ON sti.TimeNo = ti.TimeNo
        WHERE sti.ShopId = @ShopId AND ti.BegTime < 2000
        ORDER BY ti.BegTime;

        -- 构建动态列定义 (使用Unicode字符串)
        SELECT @PivotColumns = STUFF((
            SELECT
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' AND rt.CtNo = 2 THEN 1 ELSE 0 END), 0) AS ' + t.KPlusCol +
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' AND rt.AliPay > 0 THEN 1 ELSE 0 END), 0) AS ' + t.SpecialCol +
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' AND rt.MTPay > 0 THEN 1 ELSE 0 END), 0) AS ' + t.MeituanCol +
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' AND rt.DZPay > 0 THEN 1 ELSE 0 END), 0) AS ' + t.DouyinCol +
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' AND rt.CtNo = 1 THEN 1 ELSE 0 END), 0) AS ' + t.RoomFeeCol +
                N', ISNULL(SUM(CASE WHEN ti.TimeName = ' + QUOTENAME(t.TimeName, '''') + N' THEN 1 ELSE 0 END), 0) AS ' + t.SubtotalCol
            FROM #TimeSlots AS t
            ORDER BY BegTime
            FOR XML PATH(''), TYPE
        ).value('.', 'nvarchar(MAX)'), 1, 2, N'');

        -- 构建选择列定义 (使用Unicode字符串)
        SELECT @PivotSelectColumns = STUFF((
            SELECT
                N', dtp.' + t.KPlusCol + N' AS ' + QUOTENAME(N'白天_' + t.TimeName + N'_K+') +
                N', dtp.' + t.SpecialCol + N' AS ' + QUOTENAME(N'白天_' + t.TimeName + N'_特权预约') +
                N', dtp.' + t.MeituanCol + N' AS ' + QUOTENAME(N'白天_' + t.TimeName + N'_美团') +
                N', dtp.' + t.DouyinCol + N' AS ' + QUOTENAME(N'白天_' + t.TimeName + N'_抖音') +
                N', dtp.' + t.RoomFeeCol + N' AS ' + QUOTENAME(N'白天_' + t.TimeName + N'_房费') +
                N', dtp.' + t.SubtotalCol + N' AS ' + QUOTENAME(N'白天_' + t.TimeName + N'_小计')
            FROM #TimeSlots AS t
            ORDER BY BegTime
            FOR XML PATH(''), TYPE
            ).value('.', 'nvarchar(MAX)'), 1, 2, N'');

        IF @Debug = 1
        BEGIN
            PRINT N'动态列构建完成 - 耗时: ' + CAST(DATEDIFF(ms, @StepTime, GETDATE()) AS nvarchar(10)) + N'ms';
            SET @StepTime = GETDATE();
        END

        -- ====================================================================
        -- 步骤 4: 构建优化的动态 SQL (使用CTE和预处理)
        -- ====================================================================
        DECLARE @DynamicSQL nvarchar(MAX);
        
        SET @DynamicSQL = N'
        WITH
        -- CTE 1: 基础订单数据预处理 (减少重复JOIN)
        BaseOrders AS (
            SELECT
                rt.WorkDate, rt.InvNo, rt.TotalAmount, rt.Numbers, rt.CtNo,
                rt.MTPay, rt.DZPay, rt.AliPay, rt.OpenDateTime, rt.CloseDatetime,
                rt.Beg_Key, rt.End_Key,
                ti.BegTime, ti.TimeName,
                b.ShopName
            FROM dbo.RmCloseInfo_Test AS rt WITH(NOLOCK)
            JOIN MIMS.dbo.ShopInfo AS b WITH(NOLOCK) ON rt.ShopId = b.Shopid
            LEFT JOIN dbo.timeinfo AS ti WITH(NOLOCK) ON rt.Beg_Key = ti.TimeNo
            WHERE rt.ShopId = @ShopId_Param
                AND CAST(rt.WorkDate AS date) >= @BeginDate_Param
                AND CAST(rt.WorkDate AS date) <= @EndDate_Param
                AND rt.OpenDateTime IS NOT NULL
        ),
        -- CTE 2: 分类标记预处理 (优化性能)
        Classifications AS (
            SELECT
                bo.*,
                CASE
                    WHEN oc.HasBuyout = 1 THEN N''啤酒买断''
                    WHEN oc.HasChangyin = 1 THEN N''畅饮套餐''
                    WHEN oc.HasFreeConsume = 1 THEN N''自由消套餐''
                    WHEN bo.CtNo = 19 THEN N''自由餐''
                    ELSE N''其他非自由餐''
                END AS ConsumptionSubType,
                CASE
                    WHEN bo.MTPay > 0 THEN N''美团''
                    WHEN bo.DZPay > 0 THEN N''抖音''
                    WHEN bo.AliPay > 0 THEN N''特权预约''
                    WHEN bo.CtNo = 2 THEN N''K+''
                    WHEN bo.CtNo = 1 THEN N''房费''
                    ELSE N''其他''
                END AS Channel
            FROM BaseOrders bo
            LEFT JOIN #OrderClassifications oc ON bo.InvNo COLLATE DATABASE_DEFAULT = oc.InvNo
        ),
        -- CTE 3: 总览数据和直落数据
        OverviewAndDropInData AS (
            SELECT
                c.WorkDate,
                c.ShopName,
                CASE DATEPART(weekday, c.WorkDate)
                    WHEN 1 THEN N''星期日''
                    WHEN 2 THEN N''星期一''
                    WHEN 3 THEN N''星期二''
                    WHEN 4 THEN N''星期三''
                    WHEN 5 THEN N''星期四''
                    WHEN 6 THEN N''星期五''
                    WHEN 7 THEN N''星期六''
                END AS WeekdayName,
                ISNULL(SUM(c.TotalAmount), 0) AS TotalRevenue,
                ISNULL(SUM(CASE WHEN c.BegTime < 2000 THEN c.TotalAmount ELSE 0 END), 0) AS DayTimeRevenue,
                ISNULL(SUM(CASE WHEN c.BegTime >= 2000 THEN c.TotalAmount ELSE 0 END), 0) AS NightTimeRevenue,
                ISNULL(COUNT(c.InvNo), 0) AS TotalBatchCount,
                ISNULL(SUM(CASE WHEN c.BegTime < 2000 THEN 1 ELSE 0 END), 0) AS DayTimeBatchCount,
                ISNULL(SUM(CASE WHEN c.BegTime >= 2000 THEN 1 ELSE 0 END), 0) AS NightTimeBatchCount,
                -- 简化直落逻辑，提高性能
                ISNULL(SUM(CASE WHEN c.BegTime < 2000 AND c.Beg_Key <> c.End_Key AND DATEDIFF(minute, c.OpenDateTime, c.CloseDatetime) >= 180 THEN 1 ELSE 0 END), 0) AS DayTimeDropInBatch,
                ISNULL(SUM(CASE WHEN c.BegTime >= 2000 AND c.Beg_Key <> c.End_Key AND DATEDIFF(minute, c.OpenDateTime, c.CloseDatetime) >= 180 THEN 1 ELSE 0 END), 0) AS NightTimeDropInBatch,
                ISNULL(SUM(c.Numbers), 0) AS TotalGuestCount,
                ISNULL(SUM(CASE WHEN c.CtNo = 19 THEN c.Numbers ELSE 0 END), 0) AS BuffetGuestCount,
                ISNULL(SUM(CASE WHEN c.Beg_Key <> c.End_Key AND DATEDIFF(minute, c.OpenDateTime, c.CloseDatetime) >= 180 THEN c.Numbers ELSE 0 END), 0) AS TotalDropInGuests
            FROM Classifications c
            GROUP BY c.WorkDate, c.ShopName
        ),
        -- CTE 4: 白天分时段数据（动态生成）
        DayTimePivotedData AS (
            SELECT
                c.WorkDate';

        IF ISNULL(@PivotColumns, N'') <> N''
        BEGIN
            SET @DynamicSQL = @DynamicSQL + N', ' + @PivotColumns;
        END

        SET @DynamicSQL = @DynamicSQL + N'
            FROM Classifications AS c
            WHERE c.BegTime < 2000
            GROUP BY c.WorkDate
        ),
        -- CTE 5: 夜间档详细分类数据 (修复数据准确性问题)
        NightTimeDetailData AS (
            SELECT
                c.WorkDate,
                -- 自由餐统计
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由餐'' AND c.Channel = N''K+'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_KPlus,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由餐'' AND c.Channel = N''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Special,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由餐'' AND c.Channel = N''美团'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Meituan,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由餐'' AND c.Channel = N''抖音'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Douyin,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由餐'' THEN 1 ELSE 0 END), 0) AS Night_FreeMeal_Subtotal,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由餐'' THEN c.TotalAmount ELSE 0 END), 0) AS Night_FreeMeal_Amount,

                -- 啤酒买断统计（优先级最高）
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''啤酒买断'' AND c.Channel = N''K+'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_KPlus,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''啤酒买断'' AND c.Channel = N''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Special,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''啤酒买断'' AND c.Channel = N''美团'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Meituan,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''啤酒买断'' AND c.Channel = N''抖音'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Douyin,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''啤酒买断'' AND c.Channel = N''房费'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_RoomFee,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''啤酒买断'' AND c.Channel = N''其他'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Others,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''啤酒买断'' THEN 1 ELSE 0 END), 0) AS Night_BeerBuyout_Subtotal,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''啤酒买断'' THEN c.TotalAmount ELSE 0 END), 0) AS Night_BeerBuyout_Revenue,

                -- 畅饮套餐统计（次优先级）
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''畅饮套餐'' AND c.Channel = N''K+'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_KPlus,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''畅饮套餐'' AND c.Channel = N''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Special,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''畅饮套餐'' AND c.Channel = N''美团'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Meituan,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''畅饮套餐'' AND c.Channel = N''抖音'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Douyin,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''畅饮套餐'' AND c.Channel = N''房费'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_RoomFee,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''畅饮套餐'' AND c.Channel = N''其他'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Others,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''畅饮套餐'' THEN 1 ELSE 0 END), 0) AS Night_DrinkPackage_Subtotal,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''畅饮套餐'' THEN c.TotalAmount ELSE 0 END), 0) AS Night_DrinkPackage_Revenue,

                -- 自由消套餐统计（第三优先级）
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由消套餐'' AND c.Channel = N''K+'' THEN 1 ELSE 0 END), 0) AS Night_FreeConsume_KPlus,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由消套餐'' AND c.Channel = N''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_FreeConsume_Special,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由消套餐'' AND c.Channel = N''美团'' THEN 1 ELSE 0 END), 0) AS Night_FreeConsume_Meituan,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由消套餐'' AND c.Channel = N''抖音'' THEN 1 ELSE 0 END), 0) AS Night_FreeConsume_Douyin,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由消套餐'' AND c.Channel = N''房费'' THEN 1 ELSE 0 END), 0) AS Night_FreeConsume_RoomFee,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由消套餐'' AND c.Channel = N''其他'' THEN 1 ELSE 0 END), 0) AS Night_FreeConsume_Others,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由消套餐'' THEN 1 ELSE 0 END), 0) AS Night_FreeConsume_Subtotal,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''自由消套餐'' THEN c.TotalAmount ELSE 0 END), 0) AS Night_FreeConsume_Revenue,

                -- 其他非自由餐统计（兜底分类）
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''其他非自由餐'' AND c.Channel = N''K+'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_KPlus,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''其他非自由餐'' AND c.Channel = N''特权预约'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Special,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''其他非自由餐'' AND c.Channel = N''美团'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Meituan,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''其他非自由餐'' AND c.Channel = N''抖音'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Douyin,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''其他非自由餐'' AND c.Channel = N''房费'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_RoomFee,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''其他非自由餐'' AND c.Channel = N''其他'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Others,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''其他非自由餐'' THEN 1 ELSE 0 END), 0) AS Night_OtherNonFree_Subtotal,
                ISNULL(SUM(CASE WHEN c.ConsumptionSubType = N''其他非自由餐'' THEN c.TotalAmount ELSE 0 END), 0) AS Night_OtherNonFree_Revenue
            FROM Classifications c
            WHERE c.BegTime >= 2000
            GROUP BY c.WorkDate
        )
        -- 最终联接所有 CTE 的结果 (使用Unicode列名)
        SELECT
            ovd.WorkDate AS N''日期'',
            ovd.ShopName AS N''门店'',
            ovd.WeekdayName AS N''星期'',
            -- 总览指标
            ISNULL(ovd.TotalRevenue, 0) AS N''营收_总收入'',
            ISNULL(ovd.DayTimeRevenue, 0) AS N''营收_白天档'',
            ISNULL(ovd.NightTimeRevenue, 0) AS N''营收_晚上档'',
            ISNULL(ovd.TotalBatchCount, 0) AS N''带客_全天总批数'',
            ISNULL(ovd.DayTimeBatchCount, 0) AS N''带客_白天档_总批次'',
            ISNULL(ovd.NightTimeBatchCount, 0) AS N''带客_晚上档_总批次'',
            ISNULL(ovd.DayTimeDropInBatch, 0) AS N''带客_白天档_直落'',
            ISNULL(ovd.NightTimeDropInBatch, 0) AS N''带客_晚上档_直落'',
            ISNULL(ovd.TotalGuestCount, 0) AS N''用餐_总人数'',
            ISNULL(ovd.BuffetGuestCount, 0) AS N''用餐_自助餐人数'',
            ISNULL(ovd.TotalDropInGuests, 0) AS N''用餐_直落人数''';

        IF ISNULL(@PivotSelectColumns, N'') <> N''
        BEGIN
            SET @DynamicSQL = @DynamicSQL + N', ' + @PivotSelectColumns;
        END

        SET @DynamicSQL = @DynamicSQL + N'
            -- 自由餐统计
            , ISNULL(ntd.Night_FreeMeal_KPlus, 0) AS N''晚间_自由餐_K+''
            , ISNULL(ntd.Night_FreeMeal_Special, 0) AS N''晚间_自由餐_特权预约''
            , ISNULL(ntd.Night_FreeMeal_Meituan, 0) AS N''晚间_自由餐_美团''
            , ISNULL(ntd.Night_FreeMeal_Douyin, 0) AS N''晚间_自由餐_抖音''
            , ISNULL(ntd.Night_FreeMeal_Subtotal, 0) AS N''晚间_自由餐_小计''
            , ISNULL(ntd.Night_FreeMeal_Amount, 0) AS N''晚间_自由餐_消费金额''

            -- 啤酒买断统计
            , ISNULL(ntd.Night_BeerBuyout_KPlus, 0) AS N''晚间_啤酒买断_K+''
            , ISNULL(ntd.Night_BeerBuyout_Special, 0) AS N''晚间_啤酒买断_特权预约''
            , ISNULL(ntd.Night_BeerBuyout_Meituan, 0) AS N''晚间_啤酒买断_美团''
            , ISNULL(ntd.Night_BeerBuyout_Douyin, 0) AS N''晚间_啤酒买断_抖音''
            , ISNULL(ntd.Night_BeerBuyout_RoomFee, 0) AS N''晚间_啤酒买断_房费''
            , ISNULL(ntd.Night_BeerBuyout_Others, 0) AS N''晚间_啤酒买断_其他''
            , ISNULL(ntd.Night_BeerBuyout_Subtotal, 0) AS N''晚间_啤酒买断_小计''
            , ISNULL(ntd.Night_BeerBuyout_Revenue, 0) AS N''晚间_啤酒买断_营业额''

            -- 畅饮套餐统计
            , ISNULL(ntd.Night_DrinkPackage_KPlus, 0) AS N''晚间_畅饮套餐_K+''
            , ISNULL(ntd.Night_DrinkPackage_Special, 0) AS N''晚间_畅饮套餐_特权预约''
            , ISNULL(ntd.Night_DrinkPackage_Meituan, 0) AS N''晚间_畅饮套餐_美团''
            , ISNULL(ntd.Night_DrinkPackage_Douyin, 0) AS N''晚间_畅饮套餐_抖音''
            , ISNULL(ntd.Night_DrinkPackage_RoomFee, 0) AS N''晚间_畅饮套餐_房费''
            , ISNULL(ntd.Night_DrinkPackage_Others, 0) AS N''晚间_畅饮套餐_其他''
            , ISNULL(ntd.Night_DrinkPackage_Subtotal, 0) AS N''晚间_畅饮套餐_小计''
            , ISNULL(ntd.Night_DrinkPackage_Revenue, 0) AS N''晚间_畅饮套餐_营业额''

            -- 自由消套餐统计
            , ISNULL(ntd.Night_FreeConsume_KPlus, 0) AS N''晚间_自由消套餐_K+''
            , ISNULL(ntd.Night_FreeConsume_Special, 0) AS N''晚间_自由消套餐_特权预约''
            , ISNULL(ntd.Night_FreeConsume_Meituan, 0) AS N''晚间_自由消套餐_美团''
            , ISNULL(ntd.Night_FreeConsume_Douyin, 0) AS N''晚间_自由消套餐_抖音''
            , ISNULL(ntd.Night_FreeConsume_RoomFee, 0) AS N''晚间_自由消套餐_房费''
            , ISNULL(ntd.Night_FreeConsume_Others, 0) AS N''晚间_自由消套餐_其他''
            , ISNULL(ntd.Night_FreeConsume_Subtotal, 0) AS N''晚间_自由消套餐_小计''
            , ISNULL(ntd.Night_FreeConsume_Revenue, 0) AS N''晚间_自由消套餐_营业额''

            -- 其他非自由餐统计
            , ISNULL(ntd.Night_OtherNonFree_KPlus, 0) AS N''晚间_其他非自由餐_K+''
            , ISNULL(ntd.Night_OtherNonFree_Special, 0) AS N''晚间_其他非自由餐_特权预约''
            , ISNULL(ntd.Night_OtherNonFree_Meituan, 0) AS N''晚间_其他非自由餐_美团''
            , ISNULL(ntd.Night_OtherNonFree_Douyin, 0) AS N''晚间_其他非自由餐_抖音''
            , ISNULL(ntd.Night_OtherNonFree_RoomFee, 0) AS N''晚间_其他非自由餐_房费''
            , ISNULL(ntd.Night_OtherNonFree_Others, 0) AS N''晚间_其他非自由餐_其他''
            , ISNULL(ntd.Night_OtherNonFree_Subtotal, 0) AS N''晚间_其他非自由餐_小计''
            , ISNULL(ntd.Night_OtherNonFree_Revenue, 0) AS N''晚间_其他非自由餐_营业额''
        FROM
            OverviewAndDropInData AS ovd
        LEFT JOIN
            DayTimePivotedData AS dtp ON ovd.WorkDate = dtp.WorkDate
        LEFT JOIN
            NightTimeDetailData AS ntd ON ovd.WorkDate = ntd.WorkDate
        ORDER BY
            ovd.WorkDate;
        ';

        -- 打印动态 SQL 进行调试 (如果开启调试模式)
        IF @Debug = 1
        BEGIN
            PRINT N'动态SQL构建完成 - 总耗时: ' + CAST(DATEDIFF(ms, @StartTime, GETDATE()) AS nvarchar(10)) + N'ms';
            PRINT N'执行动态SQL...';
            PRINT SUBSTRING(@DynamicSQL, 1, 4000); -- 打印前4000个字符用于调试
        END

        -- 使用 sp_executesql 执行动态 SQL，并传入参数
        EXEC sp_executesql @DynamicSQL,
            N'@BeginDate_Param date, @EndDate_Param date, @ShopId_Param int',
            @BeginDate_Param = @BeginDate,
            @EndDate_Param = @EndDate,
            @ShopId_Param = @ShopId;

        -- 清理临时表
        IF OBJECT_ID('tempdb..#OrderClassifications') IS NOT NULL DROP TABLE #OrderClassifications;
        IF OBJECT_ID('tempdb..#TimeSlots') IS NOT NULL DROP TABLE #TimeSlots;

        IF @Debug = 1
        BEGIN
            PRINT N'存储过程执行完成 - 总耗时: ' + CAST(DATEDIFF(ms, @StartTime, GETDATE()) AS nvarchar(10)) + N'ms';
        END

    END TRY
    BEGIN CATCH
        -- 错误处理
        DECLARE @ErrorMessage nvarchar(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity int = ERROR_SEVERITY();
        DECLARE @ErrorState int = ERROR_STATE();

        -- 清理临时表
        IF OBJECT_ID('tempdb..#OrderClassifications') IS NOT NULL DROP TABLE #OrderClassifications;
        IF OBJECT_ID('tempdb..#TimeSlots') IS NOT NULL DROP TABLE #TimeSlots;

        PRINT N'存储过程执行出错: ' + @ErrorMessage;
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
        RETURN;
    END CATCH
END
GO
