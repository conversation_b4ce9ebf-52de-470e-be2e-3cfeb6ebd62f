-- ====================================================================
-- KTV每日报告存储过程 - 简化版本（用户提供的正确版本）
-- 只包含两个主要分类：K+自由餐 和 20点后进场套餐
-- 创建时间: 2025-01-23
-- ====================================================================

USE OperateData;
GO

-- 如果存储过程已存在，则先删除
IF OBJECT_ID('dbo.usp_GenerateSimplifiedDailyReport', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_GenerateSimplifiedDailyReport;
GO

CREATE PROCEDURE dbo.usp_GenerateSimplifiedDailyReport
    @ShopId int = 0,
    @BeginDate date = NULL,
    @EndDate date = NULL,
    @Debug bit = 0
AS
BEGIN
    SET NOCOUNT ON;
    SET QUOTED_IDENTIFIER ON;

    -- 参数默认值处理
    IF @BeginDate IS NULL SET @BeginDate = DATEADD(day, -1, GETDATE());
    IF @EndDate IS NULL SET @EndDate = DATEADD(day, -1, GETDATE());

    -- 参数验证
    IF @ShopId <= 0
    BEGIN
        RAISERROR(N'门店ID必须大于0', 16, 1);
        RETURN;
    END

    BEGIN TRY
        -- 主查询：简化版本，只包含两个主要分类
        WITH 
        -- CTE 1: 基础数据预处理
        BaseData AS (
            SELECT
                rt.WorkDate, rt.InvNo, rt.TotalAmount, rt.Numbers, rt.CtNo,
                rt.MTPay, rt.DZPay, rt.AliPay, rt.OpenDateTime, rt.CloseDatetime,
                b.ShopName,
                DATENAME(weekday, CAST(rt.WorkDate AS date)) AS WeekdayName
            FROM dbo.RmCloseInfo_Test AS rt WITH(NOLOCK)
            JOIN MIMS.dbo.ShopInfo AS b WITH(NOLOCK) ON rt.ShopId = b.Shopid
            WHERE rt.ShopId = @ShopId
                AND CAST(rt.WorkDate AS date) >= @BeginDate
                AND CAST(rt.WorkDate AS date) <= @EndDate
                AND rt.OpenDateTime IS NOT NULL
        ),
        -- CTE 2: 总览统计
        OverviewData AS (
            SELECT
                bd.WorkDate,
                bd.ShopName,
                bd.WeekdayName,
                SUM(bd.TotalAmount) AS TotalRevenue,
                SUM(CASE WHEN DATEPART(hour, bd.OpenDateTime) < 20 THEN bd.TotalAmount ELSE 0 END) AS DayTimeRevenue,
                SUM(CASE WHEN DATEPART(hour, bd.OpenDateTime) >= 20 THEN bd.TotalAmount ELSE 0 END) AS NightTimeRevenue,
                COUNT(bd.InvNo) AS TotalBatchCount,
                COUNT(CASE WHEN DATEPART(hour, bd.OpenDateTime) < 20 THEN 1 ELSE NULL END) AS DayTimeBatchCount,
                COUNT(CASE WHEN DATEPART(hour, bd.OpenDateTime) >= 20 THEN 1 ELSE NULL END) AS NightTimeBatchCount,
                SUM(bd.Numbers) AS TotalGuestCount,
                SUM(bd.Numbers) - SUM(CASE WHEN bd.CtNo = 1 THEN bd.Numbers ELSE 0 END) AS BuffetGuestCount
            FROM BaseData AS bd
            GROUP BY bd.WorkDate, bd.ShopName, bd.WeekdayName
        ),
        -- CTE 3: 夜间档简化统计（只包含两个主要分类）
        NightSimplifiedData AS (
            SELECT
                rt.WorkDate,
                
                -- K+自由餐统计 (CtNo=19表示自由餐)
                COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.CtNo = 2 THEN rt.InvNo ELSE NULL END) AS FreeMeal_KPlus,
                COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.AliPay > 0 THEN rt.InvNo ELSE NULL END) AS FreeMeal_Special,
                COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.MTPay > 0 THEN rt.InvNo ELSE NULL END) AS FreeMeal_Meituan,
                COUNT(DISTINCT CASE WHEN rt.CtNo = 19 AND rt.DZPay > 0 THEN rt.InvNo ELSE NULL END) AS FreeMeal_Douyin,
                COUNT(DISTINCT CASE WHEN rt.CtNo = 19 THEN rt.InvNo ELSE NULL END) AS FreeMeal_BatchCount,
                SUM(CASE WHEN rt.CtNo = 19 THEN rt.TotalAmount ELSE 0 END) AS FreeMeal_Revenue,

                -- 20点后进场套餐统计（正价套餐 + 促销套餐）
                -- 买断套餐 
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.CtNo = 2 THEN rt.InvNo ELSE NULL END) AS Buyout_KPlus,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.AliPay > 0 THEN rt.InvNo ELSE NULL END) AS Buyout_Special,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.MTPay > 0 THEN rt.InvNo ELSE NULL END) AS Buyout_Meituan,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.DZPay > 0 THEN rt.InvNo ELSE NULL END) AS Buyout_Douyin,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.CtNo = 1 THEN rt.InvNo ELSE NULL END) AS Buyout_RoomFee,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' AND rt.CtNo NOT IN (1,2,19) AND rt.AliPay = 0 AND rt.MTPay = 0 AND rt.DZPay = 0 THEN rt.InvNo ELSE NULL END) AS Buyout_Others,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%买断%' THEN rt.InvNo ELSE NULL END) AS Buyout_BatchCount,
                SUM(CASE WHEN fcb.FdCName LIKE N'%买断%' THEN rt.TotalAmount ELSE 0 END) AS Buyout_Revenue,

                -- 畅饮套餐
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.CtNo = 2 THEN rt.InvNo ELSE NULL END) AS Changyin_KPlus,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.AliPay > 0 THEN rt.InvNo ELSE NULL END) AS Changyin_Special,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.MTPay > 0 THEN rt.InvNo ELSE NULL END) AS Changyin_Meituan,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.DZPay > 0 THEN rt.InvNo ELSE NULL END) AS Changyin_Douyin,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.CtNo = 1 THEN rt.InvNo ELSE NULL END) AS Changyin_RoomFee,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' AND rt.CtNo NOT IN (1,2,19) AND rt.AliPay = 0 AND rt.MTPay = 0 AND rt.DZPay = 0 THEN rt.InvNo ELSE NULL END) AS Changyin_Others,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN rt.InvNo ELSE NULL END) AS Changyin_BatchCount,
                SUM(CASE WHEN fcb.FdCName LIKE N'%畅饮%' THEN rt.TotalAmount ELSE 0 END) AS Changyin_Revenue,

                -- 自由消套餐
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.CtNo = 2 THEN rt.InvNo ELSE NULL END) AS FreeConsumption_KPlus,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.AliPay > 0 THEN rt.InvNo ELSE NULL END) AS FreeConsumption_Special,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.MTPay > 0 THEN rt.InvNo ELSE NULL END) AS FreeConsumption_Meituan,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.DZPay > 0 THEN rt.InvNo ELSE NULL END) AS FreeConsumption_Douyin,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.CtNo = 1 THEN rt.InvNo ELSE NULL END) AS FreeConsumption_RoomFee,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' AND rt.CtNo NOT IN (1,2,19) AND rt.AliPay = 0 AND rt.MTPay = 0 AND rt.DZPay = 0 THEN rt.InvNo ELSE NULL END) AS FreeConsumption_Others,
                COUNT(DISTINCT CASE WHEN fcb.FdCName LIKE N'%自由消%' THEN rt.InvNo ELSE NULL END) AS FreeConsumption_BatchCount,
                SUM(CASE WHEN fcb.FdCName LIKE N'%自由消%' THEN rt.TotalAmount ELSE 0 END) AS FreeConsumption_Revenue,

                -- 20点后非套餐分类（CtNo<>19 且不匹配任何套餐）
                COUNT(DISTINCT CASE WHEN rt.CtNo <> 19
                    AND (fcb.FdCName IS NULL OR (fcb.FdCName NOT LIKE N'%买断%' AND fcb.FdCName NOT LIKE N'%畅饮%' AND fcb.FdCName NOT LIKE N'%自由消%'))
                    AND rt.AliPay > 0 THEN rt.InvNo ELSE NULL END) AS NonPackage_Special,
                COUNT(DISTINCT CASE WHEN rt.CtNo <> 19
                    AND (fcb.FdCName IS NULL OR (fcb.FdCName NOT LIKE N'%买断%' AND fcb.FdCName NOT LIKE N'%畅饮%' AND fcb.FdCName NOT LIKE N'%自由消%'))
                    AND rt.MTPay > 0 THEN rt.InvNo ELSE NULL END) AS NonPackage_Meituan,
                COUNT(DISTINCT CASE WHEN rt.CtNo <> 19
                    AND (fcb.FdCName IS NULL OR (fcb.FdCName NOT LIKE N'%买断%' AND fcb.FdCName NOT LIKE N'%畅饮%' AND fcb.FdCName NOT LIKE N'%自由消%'))
                    AND rt.DZPay > 0 THEN rt.InvNo ELSE NULL END) AS NonPackage_Douyin,
                COUNT(DISTINCT CASE WHEN rt.CtNo <> 19
                    AND (fcb.FdCName IS NULL OR (fcb.FdCName NOT LIKE N'%买断%' AND fcb.FdCName NOT LIKE N'%畅饮%' AND fcb.FdCName NOT LIKE N'%自由消%'))
                    AND rt.AliPay = 0 AND rt.MTPay = 0 AND rt.DZPay = 0 THEN rt.InvNo ELSE NULL END) AS NonPackage_Others

            FROM dbo.RmCloseInfo_Test AS rt WITH(NOLOCK)
            LEFT JOIN dbo.FdCashBak AS fcb WITH(NOLOCK) ON rt.InvNo COLLATE DATABASE_DEFAULT = fcb.InvNo COLLATE DATABASE_DEFAULT
            WHERE rt.ShopId = @ShopId
                AND CAST(rt.WorkDate AS date) >= @BeginDate
                AND CAST(rt.WorkDate AS date) <= @EndDate
                AND rt.OpenDateTime IS NOT NULL
                AND DATEPART(hour, rt.OpenDateTime) >= 20  -- 夜间档筛选
            GROUP BY rt.WorkDate
        )
        -- 最终结果（简化版本，只包含关键字段）
        SELECT
            od.WorkDate AS N'日期',
            od.ShopName AS N'门店',
            od.WeekdayName AS N'星期',
            ISNULL(od.TotalRevenue, 0) AS N'营收_总收入',
            ISNULL(od.DayTimeRevenue, 0) AS N'营收_白天档',
            ISNULL(od.NightTimeRevenue, 0) AS N'营收_晚上档',
            ISNULL(od.TotalBatchCount, 0) AS N'带客_全天总批数',
            ISNULL(od.DayTimeBatchCount, 0) AS N'带客_白天档_总批次',
            ISNULL(od.NightTimeBatchCount, 0) AS N'带客_晚上档_总批次',

            -- K+自由餐分类
            ISNULL(nsd.FreeMeal_KPlus, 0) AS N'K+自由餐_K+',
            ISNULL(nsd.FreeMeal_Special, 0) AS N'K+自由餐_特权',
            ISNULL(nsd.FreeMeal_Meituan, 0) AS N'K+自由餐_美团',
            ISNULL(nsd.FreeMeal_Douyin, 0) AS N'K+自由餐_抖音',
            ISNULL(nsd.FreeMeal_BatchCount, 0) AS N'K+自由餐_批数小计',
            ISNULL(nsd.FreeMeal_Revenue, 0) AS N'K+自由餐_消费金额小计',

            -- 20点后进场套餐 - 买断
            ISNULL(nsd.Buyout_BatchCount, 0) AS N'买断套餐_批次小计',
            ISNULL(nsd.Buyout_Revenue, 0) AS N'买断套餐_营收',

            -- 20点后进场套餐 - 畅饮
            ISNULL(nsd.Changyin_BatchCount, 0) AS N'畅饮套餐_批次小计',
            ISNULL(nsd.Changyin_Revenue, 0) AS N'畅饮套餐_营收',

            -- 20点后进场套餐 - 自由消
            ISNULL(nsd.FreeConsumption_BatchCount, 0) AS N'自由消套餐_批次小计',

            -- 20点后非套餐分类
            ISNULL(nsd.NonPackage_Special, 0) AS N'20点后非套餐_特权',
            ISNULL(nsd.NonPackage_Meituan, 0) AS N'20点后非套餐_美团',
            ISNULL(nsd.NonPackage_Douyin, 0) AS N'20点后非套餐_抖音',
            ISNULL(nsd.NonPackage_Others, 0) AS N'20点后非套餐_其他',

            -- 验证字段：20点后进场总计
            (ISNULL(nsd.Buyout_BatchCount, 0) + ISNULL(nsd.Changyin_BatchCount, 0) + ISNULL(nsd.FreeConsumption_BatchCount, 0) + ISNULL(nsd.NonPackage_Special, 0) + ISNULL(nsd.NonPackage_Meituan, 0) + ISNULL(nsd.NonPackage_Douyin, 0) + ISNULL(nsd.NonPackage_Others, 0)) AS N'20点后进场_总批次',
            (ISNULL(nsd.Buyout_Revenue, 0) + ISNULL(nsd.Changyin_Revenue, 0) + ISNULL(nsd.FreeConsumption_Revenue, 0)) AS N'20点后进场_总营收',

            -- 验证字段：夜间档总计验证
            (ISNULL(nsd.FreeMeal_BatchCount, 0) + ISNULL(nsd.Buyout_BatchCount, 0) + ISNULL(nsd.Changyin_BatchCount, 0) + ISNULL(nsd.FreeConsumption_BatchCount, 0) + ISNULL(nsd.NonPackage_Special, 0) + ISNULL(nsd.NonPackage_Meituan, 0) + ISNULL(nsd.NonPackage_Douyin, 0) + ISNULL(nsd.NonPackage_Others, 0)) AS N'夜间档_验证批次总计',
            (ISNULL(nsd.FreeMeal_Revenue, 0) + ISNULL(nsd.Buyout_Revenue, 0) + ISNULL(nsd.Changyin_Revenue, 0) + ISNULL(nsd.FreeConsumption_Revenue, 0)) AS N'夜间档_验证营收总计'

        FROM OverviewData AS od
        LEFT JOIN NightSimplifiedData AS nsd ON od.WorkDate = nsd.WorkDate
        ORDER BY od.WorkDate;

        -- 调试信息输出
        IF @Debug = 1
        BEGIN
            PRINT N'存储过程执行完成 - 简化版本';
            PRINT N'查询日期范围: ' + CONVERT(nvarchar(10), @BeginDate, 120) + N' 到 ' + CONVERT(nvarchar(10), @EndDate, 120);
            PRINT N'门店ID: ' + CAST(@ShopId AS nvarchar(10));
        END

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage nvarchar(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity int = ERROR_SEVERITY();
        DECLARE @ErrorState int = ERROR_STATE();

        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO
