
-- 验证直落订单的SQL查询
-- 这些是我的算法识别为直落的订单，请检查是否准确

-- 1. 查看结账数据和估算的开台时间
SELECT 
    c.InvNo as '订单号',
    c.WorkDate as '营业日期',
    c.CloseDatetime as '结账时间',
    DATEADD(HOUR, -2.5, c.CloseDatetime) as '估算开台时间',
    c.<PERSON> as '金额',
    c.<PERSON> as '支付方式'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '20250716'
    AND c.InvNo IN ('A02426691', 'A02426700', 'A02426697', 'A02426698', 'A02426695', 'A02426696', 'A02426699', 'A02426700', 'A02426697', 'A02426698', 'A02426695', 'A02426696', 'A02426699', 'A02426693', 'A02426694', 'A02426714', 'A02426705', 'A02426715', 'A02426711', 'A02426713', 'A02426712', 'A02426710', 'A02426708', 'A02426707', 'A02426714', 'A02426705', 'A02426715', 'A02426711', 'A02426713', 'A02426712', 'A02426710', 'A02426708', 'A02426707', 'A02426703', 'A02426709', 'A02426704', 'A02426719', 'A02426725', 'A02426719', 'A02426725', 'A02426723', 'A02426717', 'A02426722', 'A02426720', 'A02426724', 'A02426701', 'A02426721', 'A02426718', 'A02426730', 'A02426716', 'A02426727', 'A02426716', 'A02426727', 'A02426739', 'A02426737', 'A02426734', 'A02426732', 'A02426733', 'A02426740', 'A02426738', 'A02426731', 'A02426735', 'A02426742', 'A02426743', 'A02426736', 'A02426745', 'A02426728', 'A02426771', 'A02426729', 'A02426744', 'A02426716', 'A02426727', 'A02426739', 'A02426737', 'A02426734', 'A02426732', 'A02426733', 'A02426740', 'A02426738', 'A02426731', 'A02426735', 'A02426742', 'A02426743', 'A02426736', 'A02426745', 'A02426728', 'A02426771', 'A02426729', 'A02426744', 'A02426757', 'A02426759', 'A02426751', 'A02426758', 'A02426746', 'A02426749', 'A02426760', 'A02426756', 'A02426750', 'A02426747', 'A02426754', 'A02426753', 'A02426752', 'A02426748', 'A02426755', 'A02426739', 'A02426737', 'A02426734', 'A02426732', 'A02426733', 'A02426740', 'A02426738', 'A02426731', 'A02426735', 'A02426742', 'A02426743', 'A02426736', 'A02426745', 'A02426728', 'A02426771', 'A02426729', 'A02426744', 'A02426757', 'A02426759', 'A02426751', 'A02426758', 'A02426746', 'A02426749', 'A02426760', 'A02426756', 'A02426750', 'A02426747', 'A02426754', 'A02426753', 'A02426752', 'A02426748', 'A02426755', 'A02426739', 'A02426737', 'A02426734', 'A02426732', 'A02426733', 'A02426740', 'A02426738', 'A02426731', 'A02426735', 'A02426742', 'A02426743', 'A02426736', 'A02426745', 'A02426728', 'A02426771', 'A02426729', 'A02426744', 'A02426757', 'A02426759', 'A02426751', 'A02426758', 'A02426746', 'A02426749', 'A02426760', 'A02426756', 'A02426750', 'A02426747', 'A02426754', 'A02426753', 'A02426752', 'A02426748', 'A02426755', 'A02426728', 'A02426771', 'A02426729', 'A02426744', 'A02426757', 'A02426759', 'A02426751', 'A02426758', 'A02426746', 'A02426749', 'A02426760', 'A02426756', 'A02426750', 'A02426747', 'A02426754', 'A02426753', 'A02426752', 'A02426748', 'A02426755', 'A02426772', 'A02426762', 'A02426768', 'A02426763', 'A02426769', 'A02426770', 'A02426765', 'A02426764', 'A02426767', 'A02426766', 'A02426741', 'A02426706')
ORDER BY c.CloseDatetime;

-- 2. 如果有开台数据，查看实际开台时间进行对比
SELECT 
    o.BookNo as '开台号',
    o.CustName as '客户名称',
    o.ComeDate as '开台日期',
    o.ComeTime as '开台时间',
    o.RmNo as '房间号',
    CONVERT(datetime, o.ComeDate + ' ' + o.ComeTime) as '开台时间戳'
FROM opencacheinfo o
WHERE o.shopid = 11 
    AND o.ComeDate = '20250716'
ORDER BY o.ComeTime;

-- 3. 时间段配置
SELECT 
    t.TimeName as '时间段名称',
    t.BegTime as '开始时间',
    t.EndTime as '结束时间',
    CASE 
        WHEN t.EndTime < t.BegTime THEN '跨天时间段'
        ELSE '当天时间段'
    END as '时间段类型'
FROM shoptimeinfo st
LEFT JOIN timeinfo t ON st.timeno = t.timeno
WHERE st.shopid = 11
ORDER BY t.BegTime;

-- 4. 详细分析：按时间段查看重叠订单
WITH TimeSlots AS (
    SELECT 
        t.TimeName,
        t.BegTime,
        t.EndTime,
        CASE 
            WHEN t.EndTime < t.BegTime THEN 
                CONVERT(datetime, '20250716 ' + FORMAT(t.BegTime/100, '00') + ':' + FORMAT(t.BegTime%100, '00'))
            ELSE 
                CONVERT(datetime, '20250716 ' + FORMAT(t.BegTime/100, '00') + ':' + FORMAT(t.BegTime%100, '00'))
        END as SlotStart,
        CASE 
            WHEN t.EndTime < t.BegTime THEN 
                CONVERT(datetime, DATEADD(day, 1, '20250716') + ' ' + FORMAT(t.EndTime/100, '00') + ':' + FORMAT(t.EndTime%100, '00'))
            ELSE 
                CONVERT(datetime, '20250716 ' + FORMAT(t.EndTime/100, '00') + ':' + FORMAT(t.EndTime%100, '00'))
        END as SlotEnd
    FROM shoptimeinfo st
    LEFT JOIN timeinfo t ON st.timeno = t.timeno
    WHERE st.shopid = 11 AND t.BegTime IS NOT NULL
),
OrdersWithEstimatedOpen AS (
    SELECT 
        InvNo,
        CloseDatetime,
        DATEADD(HOUR, -2.5, CloseDatetime) as EstimatedOpen,
        Tot
    FROM rmcloseinfo
    WHERE shopid = 11 AND WorkDate = '20250716'
        AND InvNo IN ('A02426691', 'A02426700', 'A02426697', 'A02426698', 'A02426695', 'A02426696', 'A02426699', 'A02426700', 'A02426697', 'A02426698', 'A02426695', 'A02426696', 'A02426699', 'A02426693', 'A02426694', 'A02426714', 'A02426705', 'A02426715', 'A02426711', 'A02426713', 'A02426712', 'A02426710', 'A02426708', 'A02426707', 'A02426714', 'A02426705', 'A02426715', 'A02426711', 'A02426713', 'A02426712', 'A02426710', 'A02426708', 'A02426707', 'A02426703', 'A02426709', 'A02426704', 'A02426719', 'A02426725', 'A02426719', 'A02426725', 'A02426723', 'A02426717', 'A02426722', 'A02426720', 'A02426724', 'A02426701', 'A02426721', 'A02426718', 'A02426730', 'A02426716', 'A02426727', 'A02426716', 'A02426727', 'A02426739', 'A02426737', 'A02426734', 'A02426732', 'A02426733', 'A02426740', 'A02426738', 'A02426731', 'A02426735', 'A02426742', 'A02426743', 'A02426736', 'A02426745', 'A02426728', 'A02426771', 'A02426729', 'A02426744', 'A02426716', 'A02426727', 'A02426739', 'A02426737', 'A02426734', 'A02426732', 'A02426733', 'A02426740', 'A02426738', 'A02426731', 'A02426735', 'A02426742', 'A02426743', 'A02426736', 'A02426745', 'A02426728', 'A02426771', 'A02426729', 'A02426744', 'A02426757', 'A02426759', 'A02426751', 'A02426758', 'A02426746', 'A02426749', 'A02426760', 'A02426756', 'A02426750', 'A02426747', 'A02426754', 'A02426753', 'A02426752', 'A02426748', 'A02426755', 'A02426739', 'A02426737', 'A02426734', 'A02426732', 'A02426733', 'A02426740', 'A02426738', 'A02426731', 'A02426735', 'A02426742', 'A02426743', 'A02426736', 'A02426745', 'A02426728', 'A02426771', 'A02426729', 'A02426744', 'A02426757', 'A02426759', 'A02426751', 'A02426758', 'A02426746', 'A02426749', 'A02426760', 'A02426756', 'A02426750', 'A02426747', 'A02426754', 'A02426753', 'A02426752', 'A02426748', 'A02426755', 'A02426739', 'A02426737', 'A02426734', 'A02426732', 'A02426733', 'A02426740', 'A02426738', 'A02426731', 'A02426735', 'A02426742', 'A02426743', 'A02426736', 'A02426745', 'A02426728', 'A02426771', 'A02426729', 'A02426744', 'A02426757', 'A02426759', 'A02426751', 'A02426758', 'A02426746', 'A02426749', 'A02426760', 'A02426756', 'A02426750', 'A02426747', 'A02426754', 'A02426753', 'A02426752', 'A02426748', 'A02426755', 'A02426728', 'A02426771', 'A02426729', 'A02426744', 'A02426757', 'A02426759', 'A02426751', 'A02426758', 'A02426746', 'A02426749', 'A02426760', 'A02426756', 'A02426750', 'A02426747', 'A02426754', 'A02426753', 'A02426752', 'A02426748', 'A02426755', 'A02426772', 'A02426762', 'A02426768', 'A02426763', 'A02426769', 'A02426770', 'A02426765', 'A02426764', 'A02426767', 'A02426766', 'A02426741', 'A02426706')
)
SELECT 
    ts.TimeName as '时间段',
    FORMAT(ts.SlotStart, 'HH:mm') + '-' + FORMAT(ts.SlotEnd, 'HH:mm') as '时间范围',
    o.InvNo as '订单号',
    FORMAT(o.EstimatedOpen, 'yyyy-MM-dd HH:mm') as '估算开台时间',
    FORMAT(o.CloseDatetime, 'yyyy-MM-dd HH:mm') as '结账时间',
    o.Tot as '金额',
    CASE 
        WHEN o.EstimatedOpen < ts.SlotStart THEN '直落订单'
        ELSE '正常订单'
    END as '订单类型'
FROM TimeSlots ts
CROSS JOIN OrdersWithEstimatedOpen o
WHERE o.EstimatedOpen < ts.SlotEnd AND o.CloseDatetime > ts.SlotStart
ORDER BY ts.BegTime, o.CloseDatetime;
