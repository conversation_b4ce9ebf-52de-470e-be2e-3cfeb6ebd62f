
-- 验证直落订单的SQL查询
-- 这些是我的算法识别为直落的订单，请检查是否准确

-- 1. 查看结账数据和估算的开台时间
SELECT 
    c.InvNo as '订单号',
    c.WorkDate as '营业日期',
    c.CloseDatetime as '结账时间',
    DATEADD(HOUR, -2.5, c.CloseDatetime) as '估算开台时间',
    c.<PERSON> as '金额',
    c.<PERSON> as '支付方式'
FROM rmcloseinfo c
WHERE c.shopid = 11 
    AND c.WorkDate = '20250717'
    AND c.InvNo IN ('A02426811', 'A02426810', 'A02426807', 'A02426809', 'A02426808', 'A02426814', 'A02426813', 'A02426812', 'A02426806', 'A02426820', 'A02426819', 'A02426817', 'A02426818', 'A02426821', 'A02426826', 'A02426816', 'A02426822', 'A02426824', 'A02426823', 'A02426825', 'A02426827', 'A02426837', 'A02426836', 'A02426831', 'A02426834', 'A02426833', 'A02426835', 'A02426832', 'A02426830', 'A02426837', 'A02426836', 'A02426831', 'A02426834', 'A02426833', 'A02426835', 'A02426832', 'A02426830', 'A02426842', 'A02426829', 'A02426844', 'A02426845', 'A02426840', 'A02426853', 'A02426841', 'A02426839', 'A02426838', 'A02426843', 'A02426829', 'A02426844', 'A02426845', 'A02426840', 'A02426853', 'A02426841', 'A02426839', 'A02426838', 'A02426843', 'A02426852', 'A02426848', 'A02426846', 'A02426850', 'A02426847', 'A02426851', 'A02426844', 'A02426845', 'A02426840', 'A02426853', 'A02426841', 'A02426839', 'A02426838', 'A02426843', 'A02426852', 'A02426848', 'A02426846', 'A02426850', 'A02426847', 'A02426851', 'A02426849', 'A02426844', 'A02426845', 'A02426840', 'A02426853', 'A02426841', 'A02426839', 'A02426838', 'A02426843', 'A02426852', 'A02426848', 'A02426846', 'A02426850', 'A02426847', 'A02426851', 'A02426849', 'A02426857', 'A02426844', 'A02426845', 'A02426840', 'A02426853', 'A02426841', 'A02426839', 'A02426838', 'A02426843', 'A02426852', 'A02426848', 'A02426846', 'A02426850', 'A02426847', 'A02426851', 'A02426849', 'A02426857', 'A02426852', 'A02426848', 'A02426846', 'A02426850', 'A02426847', 'A02426851', 'A02426849', 'A02426857', 'A02426863', 'A02426859', 'A02426861', 'A02426864', 'A02426855', 'A02426856', 'A02426860', 'A02426858', 'A02426854', 'A02426862', 'A02426828', 'A02426867', 'A02426866')
ORDER BY c.CloseDatetime;

-- 2. 如果有开台数据，查看实际开台时间进行对比
SELECT 
    o.BookNo as '开台号',
    o.CustName as '客户名称',
    o.ComeDate as '开台日期',
    o.ComeTime as '开台时间',
    o.RmNo as '房间号',
    CONVERT(datetime, o.ComeDate + ' ' + o.ComeTime) as '开台时间戳'
FROM opencacheinfo o
WHERE o.shopid = 11 
    AND o.ComeDate = '20250717'
ORDER BY o.ComeTime;

-- 3. 时间段配置
SELECT 
    t.TimeName as '时间段名称',
    t.BegTime as '开始时间',
    t.EndTime as '结束时间',
    CASE 
        WHEN t.EndTime < t.BegTime THEN '跨天时间段'
        ELSE '当天时间段'
    END as '时间段类型'
FROM shoptimeinfo st
LEFT JOIN timeinfo t ON st.timeno = t.timeno
WHERE st.shopid = 11
ORDER BY t.BegTime;

-- 4. 详细分析：按时间段查看重叠订单
WITH TimeSlots AS (
    SELECT 
        t.TimeName,
        t.BegTime,
        t.EndTime,
        CASE 
            WHEN t.EndTime < t.BegTime THEN 
                CONVERT(datetime, '20250717 ' + FORMAT(t.BegTime/100, '00') + ':' + FORMAT(t.BegTime%100, '00'))
            ELSE 
                CONVERT(datetime, '20250717 ' + FORMAT(t.BegTime/100, '00') + ':' + FORMAT(t.BegTime%100, '00'))
        END as SlotStart,
        CASE 
            WHEN t.EndTime < t.BegTime THEN 
                CONVERT(datetime, DATEADD(day, 1, '20250717') + ' ' + FORMAT(t.EndTime/100, '00') + ':' + FORMAT(t.EndTime%100, '00'))
            ELSE 
                CONVERT(datetime, '20250717 ' + FORMAT(t.EndTime/100, '00') + ':' + FORMAT(t.EndTime%100, '00'))
        END as SlotEnd
    FROM shoptimeinfo st
    LEFT JOIN timeinfo t ON st.timeno = t.timeno
    WHERE st.shopid = 11 AND t.BegTime IS NOT NULL
),
OrdersWithEstimatedOpen AS (
    SELECT 
        InvNo,
        CloseDatetime,
        DATEADD(HOUR, -2.5, CloseDatetime) as EstimatedOpen,
        Tot
    FROM rmcloseinfo
    WHERE shopid = 11 AND WorkDate = '20250717'
        AND InvNo IN ('A02426811', 'A02426810', 'A02426807', 'A02426809', 'A02426808', 'A02426814', 'A02426813', 'A02426812', 'A02426806', 'A02426820', 'A02426819', 'A02426817', 'A02426818', 'A02426821', 'A02426826', 'A02426816', 'A02426822', 'A02426824', 'A02426823', 'A02426825', 'A02426827', 'A02426837', 'A02426836', 'A02426831', 'A02426834', 'A02426833', 'A02426835', 'A02426832', 'A02426830', 'A02426837', 'A02426836', 'A02426831', 'A02426834', 'A02426833', 'A02426835', 'A02426832', 'A02426830', 'A02426842', 'A02426829', 'A02426844', 'A02426845', 'A02426840', 'A02426853', 'A02426841', 'A02426839', 'A02426838', 'A02426843', 'A02426829', 'A02426844', 'A02426845', 'A02426840', 'A02426853', 'A02426841', 'A02426839', 'A02426838', 'A02426843', 'A02426852', 'A02426848', 'A02426846', 'A02426850', 'A02426847', 'A02426851', 'A02426844', 'A02426845', 'A02426840', 'A02426853', 'A02426841', 'A02426839', 'A02426838', 'A02426843', 'A02426852', 'A02426848', 'A02426846', 'A02426850', 'A02426847', 'A02426851', 'A02426849', 'A02426844', 'A02426845', 'A02426840', 'A02426853', 'A02426841', 'A02426839', 'A02426838', 'A02426843', 'A02426852', 'A02426848', 'A02426846', 'A02426850', 'A02426847', 'A02426851', 'A02426849', 'A02426857', 'A02426844', 'A02426845', 'A02426840', 'A02426853', 'A02426841', 'A02426839', 'A02426838', 'A02426843', 'A02426852', 'A02426848', 'A02426846', 'A02426850', 'A02426847', 'A02426851', 'A02426849', 'A02426857', 'A02426852', 'A02426848', 'A02426846', 'A02426850', 'A02426847', 'A02426851', 'A02426849', 'A02426857', 'A02426863', 'A02426859', 'A02426861', 'A02426864', 'A02426855', 'A02426856', 'A02426860', 'A02426858', 'A02426854', 'A02426862', 'A02426828', 'A02426867', 'A02426866')
)
SELECT 
    ts.TimeName as '时间段',
    FORMAT(ts.SlotStart, 'HH:mm') + '-' + FORMAT(ts.SlotEnd, 'HH:mm') as '时间范围',
    o.InvNo as '订单号',
    FORMAT(o.EstimatedOpen, 'yyyy-MM-dd HH:mm') as '估算开台时间',
    FORMAT(o.CloseDatetime, 'yyyy-MM-dd HH:mm') as '结账时间',
    o.Tot as '金额',
    CASE 
        WHEN o.EstimatedOpen < ts.SlotStart THEN '直落订单'
        ELSE '正常订单'
    END as '订单类型'
FROM TimeSlots ts
CROSS JOIN OrdersWithEstimatedOpen o
WHERE o.EstimatedOpen < ts.SlotEnd AND o.CloseDatetime > ts.SlotStart
ORDER BY ts.BegTime, o.CloseDatetime;
